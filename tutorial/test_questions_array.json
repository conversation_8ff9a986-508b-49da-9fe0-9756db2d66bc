[{"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is <PERSON>'s father. <PERSON> is the husband of <PERSON><PERSON> <PERSON>'s only sibling, <PERSON> (female), is married to <PERSON><PERSON> <PERSON> is <PERSON>'s only child. <PERSON> is <PERSON>'s paternal grandmother. How is <PERSON> related to <PERSON>'s son?", "choices": ["A) Father of son's wife's sister's husband", "B) Father of daughter-in-law's sister's husband", "C) Father of his son's wife's brother-in-law", "D) Father of his son's sister-in-law's husband"], "answer": "D", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "A is <PERSON>'s mother. <PERSON> is the only son of <PERSON><PERSON> <PERSON> is <PERSON>'s husband. <PERSON> has only one sibling, <PERSON> (male). <PERSON> is married to <PERSON><PERSON> <PERSON> is <PERSON>'s only child. <PERSON> is <PERSON>'s maternal grandfather. How is <PERSON> related to <PERSON>'s son-in-law?", "choices": ["A) Sister", "B) Mother", "C) Aunt", "D) Sister-in-law"], "answer": "A", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is <PERSON>'s brother. <PERSON> is the only daughter of <PERSON><PERSON> <PERSON> is married to <PERSON><PERSON> <PERSON> has only one son, <PERSON><PERSON> <PERSON>'s wife is <PERSON><PERSON>'s father is <PERSON><PERSON>'s wife is <PERSON>. How is <PERSON> related to <PERSON>'s son-in-law's father?", "choices": ["A) He is <PERSON>'s son-in-law's father", "B) Son", "C) Brother", "D) Nephew"], "answer": "A", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is <PERSON>'s paternal grandmother's only son's wife. <PERSON>'s father has no siblings. <PERSON> is <PERSON>'s husband's mother. <PERSON> is <PERSON>'s only daughter's husband. <PERSON> is <PERSON>'s son. How is Y related to <PERSON>'s paternal grandfather?", "choices": ["A) Y is <PERSON>'s paternal grandfather's nephew/niece", "B) Y is <PERSON>'s paternal grandfather's son/daughter", "C) Y is <PERSON>'s paternal grandfather's grandchild", "D) Y is <PERSON>'s paternal grandfather's sibling's child"], "answer": "C", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON>'s father, <PERSON>, has only one sibling, <PERSON> (female). <PERSON>'s husband is <PERSON>. <PERSON>'s only child is <PERSON><PERSON><PERSON>. E<PERSON><PERSON>'s maternal grandmother is <PERSON><PERSON>. <PERSON><PERSON>'s husband is <PERSON><PERSON>. How is <PERSON> related to <PERSON><PERSON>'s son-in-law?", "choices": ["A) Nephew/Niece", "B) Son/Daughter", "C) Cousin", "D) Grandchild"], "answer": "A", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is the mother of <PERSON><PERSON> <PERSON> is the only child of her parents. <PERSON> is <PERSON>'s husband. <PERSON> is <PERSON>'s only sibling (male). <PERSON> is married to <PERSON><PERSON> <PERSON> is <PERSON>'s father. <PERSON> is <PERSON>'s wife's only son-in-law. How is <PERSON> related to <PERSON>?", "choices": ["A) Mother of <PERSON>'s wife's brother's wife", "B) Mother-in-law of <PERSON>'s brother-in-law", "C) Mother of <PERSON>'s wife's sister-in-law", "D) Mother-in-law of <PERSON>'s wife's brother"], "answer": "D", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is <PERSON><PERSON>'s paternal uncle. B1's mother is C1. <PERSON><PERSON> has two siblings: D1 (male) and E1 (female). D1 is unmarried. <PERSON>1 is married to <PERSON> and they have a son G1. H1 is <PERSON><PERSON>'s paternal grandmother. How is <PERSON> related to <PERSON><PERSON>'s son?", "choices": ["A) Brother-in-law's brother", "B) Brother of son's wife's maternal uncle's father-in-law", "C) Brother of nephew's maternal aunt's husband", "D) No direct relation or cannot be determined"], "answer": "A", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON>'s father <PERSON> is married to <PERSON><PERSON>'s only sister <PERSON> is married to <PERSON><PERSON> has only one child <PERSON><PERSON> <PERSON>'s paternal grandfather is <PERSON><PERSON> <PERSON>'s wife <PERSON> has only one son-in-law. Who is <PERSON> to <PERSON>'s son-in-law's child?", "choices": ["A) Paternal cousin", "B) Maternal cousin", "C) Sibling", "D) <PERSON>'s gender is unknown, so relationship to <PERSON> cannot be fully specified beyond cousin"], "answer": "A", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is <PERSON>'s wife. <PERSON> is the only son of <PERSON><PERSON> <PERSON> is married to <PERSON><PERSON> <PERSON> has only one daughter, <PERSON><PERSON> <PERSON> is married to <PERSON><PERSON> <PERSON> is <PERSON>'s son. <PERSON> is <PERSON>'s paternal grandmother's husband's only sibling (male). How is <PERSON> related to <PERSON>?", "choices": ["A) <PERSON><PERSON><PERSON>-in-law (husband's uncle's wife)", "B) Sister-in-law (husband's aunt's husband's sister)", "C) Wife of <PERSON>'s nephew", "D) Wife of <PERSON>'s grandnephew"], "answer": "C", "explanation": ""}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is <PERSON>'s father. <PERSON> is <PERSON>'s husband. <PERSON> is the only daughter of <PERSON><PERSON> <PERSON> is married to <PERSON><PERSON> <PERSON> has one son <PERSON> and one daughter <PERSON><PERSON> <PERSON> is married to <PERSON><PERSON> <PERSON> is <PERSON>'s son. <PERSON> is <PERSON>'s paternal grandmother. How is <PERSON> related to <PERSON>'s husband?", "choices": ["A) Father of <PERSON>'s husband's son's wife's father-in-law", "B) Father-in-law of <PERSON>'s husband's daughter-in-law's brother", "C) Father of the husband of <PERSON>'s husband's daughter", "D) They are fathers of a married couple (co-fathers-in-law)"], "answer": "D", "explanation": ""}]