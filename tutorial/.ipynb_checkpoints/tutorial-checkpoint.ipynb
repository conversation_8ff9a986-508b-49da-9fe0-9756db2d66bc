{"cells": [{"cell_type": "markdown", "id": "f74d66b9", "metadata": {}, "source": ["### Model Adaptation & Behavior Controlling\n"]}, {"cell_type": "code", "execution_count": 3, "id": "35446362", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/jupyter-tutorial/AAIPL_129_212_190_153/tutorial\n"]}], "source": ["!pwd"]}, {"cell_type": "code", "execution_count": 5, "id": "8942edbc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting trl==0.19.0 (from -r ../default_requirements.txt (line 1))\n", "  Downloading trl-0.19.0-py3-none-any.whl.metadata (10 kB)\n", "Collecting wandb==0.20.1 (from -r ../default_requirements.txt (line 2))\n", "  Downloading wandb-0.20.1-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)\n", "Collecting ipdb==0.13.13 (from -r ../default_requirements.txt (line 3))\n", "  Downloading ipdb-0.13.13-py3-none-any.whl.metadata (14 kB)\n", "Collecting transformers==4.51.3 (from -r ../default_requirements.txt (line 4))\n", "  Downloading transformers-4.51.3-py3-none-any.whl.metadata (38 kB)\n", "Requirement already satisfied: accelerate>=1.4.0 in /usr/local/lib/python3.12/dist-packages (from trl==0.19.0->-r ../default_requirements.txt (line 1)) (1.7.0)\n", "Requirement already satisfied: datasets>=3.0.0 in /usr/local/lib/python3.12/dist-packages (from trl==0.19.0->-r ../default_requirements.txt (line 1)) (3.6.0)\n", "Requirement already satisfied: click!=8.0.0,>=7.1 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (8.1.8)\n", "Collecting gitpython!=3.1.29,>=1.0.0 (from wandb==0.20.1->-r ../default_requirements.txt (line 2))\n", "  Downloading GitPython-3.1.44-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (24.2)\n", "Requirement already satisfied: platformdirs in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (4.3.8)\n", "Requirement already satisfied: protobuf!=4.21.0,!=5.28.0,<7,>=3.19.0 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (5.29.4)\n", "Requirement already satisfied: psutil>=5.0.0 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (7.0.0)\n", "Requirement already satisfied: pydantic<3 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (2.11.4)\n", "Requirement already satisfied: pyyaml in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.0.0 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (2.32.3)\n", "Collecting sentry-sdk>=2.0.0 (from wandb==0.20.1->-r ../default_requirements.txt (line 2))\n", "  Downloading sentry_sdk-2.33.0-py2.py3-none-any.whl.metadata (10 kB)\n", "Collecting setproctitle (from wandb==0.20.1->-r ../default_requirements.txt (line 2))\n", "  Downloading setproctitle-1.3.6-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)\n", "Requirement already satisfied: typing-extensions<5,>=4.8 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r ../default_requirements.txt (line 2)) (4.13.2)\n", "Requirement already satisfied: ipython>=7.31.1 in /usr/local/lib/python3.12/dist-packages (from ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (9.3.0)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.12/dist-packages (from ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (5.2.1)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r ../default_requirements.txt (line 4)) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r ../default_requirements.txt (line 4)) (0.31.4)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r ../default_requirements.txt (line 4)) (1.26.4)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r ../default_requirements.txt (line 4)) (2024.11.6)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r ../default_requirements.txt (line 4)) (0.21.1)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r ../default_requirements.txt (line 4)) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r ../default_requirements.txt (line 4)) (4.67.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.12/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers==4.51.3->-r ../default_requirements.txt (line 4)) (2025.3.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.12/dist-packages (from pydantic<3->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.12/dist-packages (from pydantic<3->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.12/dist-packages (from pydantic<3->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (0.4.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.12/dist-packages (from requests<3,>=2.0.0->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.12/dist-packages (from requests<3,>=2.0.0->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.12/dist-packages (from requests<3,>=2.0.0->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.12/dist-packages (from requests<3,>=2.0.0->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (2025.4.26)\n", "Requirement already satisfied: torch>=2.0.0 in /usr/local/lib/python3.12/dist-packages (from accelerate>=1.4.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (2.7.0+gitf717b2a)\n", "Requirement already satisfied: pyarrow>=15.0.0 in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (20.0.0)\n", "Requirement already satisfied: dill<0.3.9,>=0.3.0 in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (0.3.8)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (2.2.3)\n", "Requirement already satisfied: xxhash in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (3.5.0)\n", "Requirement already satisfied: multiprocess<0.70.17 in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (0.70.16)\n", "Requirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in /usr/local/lib/python3.12/dist-packages (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (3.11.18)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (1.6.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (6.4.4)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (1.20.0)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.12/dist-packages (from gitpython!=3.1.29,>=1.0.0->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (4.0.12)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.12/dist-packages (from gitdb<5,>=4.0.1->gitpython!=3.1.29,>=1.0.0->wandb==0.20.1->-r ../default_requirements.txt (line 2)) (5.0.2)\n", "Requirement already satisfied: ipython-pygments-lexers in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (1.1.1)\n", "Requirement already satisfied: jedi>=0.16 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (0.19.2)\n", "Requirement already satisfied: matplotlib-inline in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (0.1.7)\n", "Requirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (4.9.0)\n", "Requirement already satisfied: prompt_toolkit<3.1.0,>=3.0.41 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (3.0.51)\n", "Requirement already satisfied: pygments>=2.4.0 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (2.19.1)\n", "Requirement already satisfied: stack_data in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (0.6.3)\n", "Requirement already satisfied: traitlets>=5.13.0 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (5.14.3)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.12/dist-packages (from prompt_toolkit<3.1.0,>=3.0.41->ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (0.2.13)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /usr/local/lib/python3.12/dist-packages (from jedi>=0.16->ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (0.8.4)\n", "Requirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.12/dist-packages (from pexpect>4.3->ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (0.7.0)\n", "Requirement already satisfied: setuptools in /usr/local/lib/python3.12/dist-packages (from torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (79.0.1)\n", "Requirement already satisfied: sympy>=1.13.3 in /usr/local/lib/python3.12/dist-packages (from torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (1.14.0)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.12/dist-packages (from torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (3.4.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.12/dist-packages (from torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (3.1.6)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.12/dist-packages (from sympy>=1.13.3->torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.12/dist-packages (from jinja2->torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (3.0.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.12/dist-packages (from pandas->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.12/dist-packages (from pandas->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.12/dist-packages (from pandas->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.12/dist-packages (from python-dateutil>=2.8.2->pandas->datasets>=3.0.0->trl==0.19.0->-r ../default_requirements.txt (line 1)) (1.17.0)\n", "Requirement already satisfied: executing>=1.2.0 in /usr/local/lib/python3.12/dist-packages (from stack_data->ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (2.2.0)\n", "Requirement already satisfied: asttokens>=2.1.0 in /usr/local/lib/python3.12/dist-packages (from stack_data->ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (3.0.0)\n", "Requirement already satisfied: pure-eval in /usr/local/lib/python3.12/dist-packages (from stack_data->ipython>=7.31.1->ipdb==0.13.13->-r ../default_requirements.txt (line 3)) (0.2.3)\n", "Downloading trl-0.19.0-py3-none-any.whl (375 kB)\n", "Downloading wandb-0.20.1-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (23.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m23.2/23.2 MB\u001b[0m \u001b[31m92.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading ipdb-0.13.13-py3-none-any.whl (12 kB)\n", "Downloading transformers-4.51.3-py3-none-any.whl (10.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.4/10.4 MB\u001b[0m \u001b[31m159.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading GitPython-3.1.44-py3-none-any.whl (207 kB)\n", "Downloading sentry_sdk-2.33.0-py2.py3-none-any.whl (356 kB)\n", "Downloading setproctitle-1.3.6-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (31 kB)\n", "Installing collected packages: setproctitle, sentry-sdk, gitpython, wandb, ipdb, transformers, trl\n", "\u001b[2K  Attempting uninstall: gitpython\n", "\u001b[2K    Found existing installation: GitPython 0.3.6\n", "\u001b[2K    Uninstalling GitPython-0.3.6:\n", "\u001b[2K      Successfully uninstalled GitPython-0.3.6━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [gitpython]\n", "\u001b[2K  Attempting uninstall: transformers[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3/7\u001b[0m [wandb]\n", "\u001b[2K    Found existing installation: transformers 4.52.2━━━━━━━━━━\u001b[0m \u001b[32m3/7\u001b[0m [wandb]\n", "\u001b[2K    Uninstalling transformers-4.52.2:[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━\u001b[0m \u001b[32m5/7\u001b[0m [transformers]\n", "\u001b[2K      Successfully uninstalled transformers-4.52.2\u001b[90m━━━━━━━━━━━\u001b[0m \u001b[32m5/7\u001b[0m [transformers]\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7/7\u001b[0m [trl]\u001b[32m6/7\u001b[0m [trl]sformers]\n", "\u001b[1A\u001b[2K\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "pypi-publisher 0.0.4 requires gitpython==0.3.6, but you have gitpython 3.1.44 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed gitpython-3.1.44 ipdb-0.13.13 sentry-sdk-2.33.0 setproctitle-1.3.6 transformers-4.51.3 trl-0.19.0 wandb-0.20.1\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["# Uncomment and execute if not done in README.ipynb\n", "!pip install -r ../default_requirements.txt"]}, {"cell_type": "code", "execution_count": 7, "id": "17479e6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/jupyter-tutorial/AAIPL_129_212_190_153/tutorial\n"]}], "source": ["# Change directory if not in tutorial\n", "import os\n", "#os.chdir(\"./tutorial\")\n", "!pwd"]}, {"cell_type": "code", "execution_count": null, "id": "440fbfc6", "metadata": {}, "outputs": [], "source": ["# os.environ['HF_TOKEN'] = \"\"\n", "# os.environ['HF_HOME']=\"\""]}, {"cell_type": "markdown", "id": "838f7fdc", "metadata": {}, "source": ["### Quality of Model Response\n", "\n", "You yourself can run the following cells and see how quality of response being affected by SFT and GRPO.\n", "\n", "<!--  -->"]}, {"cell_type": "code", "execution_count": 8, "id": "ae5b15d7", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 07-19 09:07:48 [__init__.py:248] Automatically detected platform rocm.\n", "PyTorch detected number of available devices: 1\n", "============================================================\n", "BLOOD RELATIONS GRPO TRAINER\n", "============================================================\n", "Training Type: grpo\n", "Mode: inference\n", "Model: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Output directory: checkpoints/demo/sft\n", "Dataset file: formatted_questions_array.json\n", "Learning rate: 2e-05\n", "Epochs: 3\n", "Batch size: 1\n", "LoRA rank: 8\n", "LoRA alpha: 16\n", "Max sequence length: 512\n", "GPU IDs: 0\n", "============================================================\n", "============================================================\n", "RUNNING BATCH INFERENCE\n", "============================================================\n", "Loading questions from: test_questions_array.json\n", "Successfully loaded 10 questions from JSON file\n", "Processing 10 questions...\n", "Setting up inference model...\n", "Setting up inference model on device: cuda\n", "Loading base model: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Loading tokenizer for: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Found 1 checkpoints. Using latest: checkpoints/demo/sft/checkpoint-603\n", "Loading LoRA adapters from: checkpoints/demo/sft/checkpoint-603\n", "Inference model setup completed successfully\n", "Found 1 checkpoints. Using latest: checkpoints/demo/sft/checkpoint-603\n", "Processing question 1/10...\n", "/usr/local/lib/python3.12/dist-packages/transformers/generation/configuration_utils.py:631: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.6` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`.\n", "  warnings.warn(\n", "/usr/local/lib/python3.12/dist-packages/transformers/generation/configuration_utils.py:636: UserWarning: `do_sample` is set to `False`. However, `top_p` is set to `0.9` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `top_p`.\n", "  warnings.warn(\n", "Response generated in 23.03s. Length: 136 chars.\n", "Processing question 2/10...\n", "Response generated in 1.91s. Length: 108 chars.\n", "Processing question 3/10...\n", "Response generated in 1.95s. Length: 100 chars.\n", "Processing question 4/10...\n", "Response generated in 3.34s. Length: 246 chars.\n", "Processing question 5/10...\n", "Response generated in 1.78s. Length: 117 chars.\n", "Processing question 6/10...\n", "Response generated in 3.06s. Length: 192 chars.\n", "Processing question 7/10...\n", "Response generated in 2.30s. Length: 119 chars.\n", "Processing question 8/10...\n", "Response generated in 2.19s. Length: 161 chars.\n", "Processing question 9/10...\n", "Response generated in 2.80s. Length: 174 chars.\n", "Processing question 10/10...\n", "Response generated in 13.00s. Length: 718 chars.\n", "Batch inference complete. Results saved to simple_inference.md\n", "Answer Accuracy: 4/10 (40.00%)\n", "Format Accuracy: 10/10 (100.00%)\n", "Inference model cleaned up\n", "[rank0]:[W719 09:08:47.431511609 ProcessGroupNCCL.cpp:1476] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())\n"]}], "source": ["# Base model:\n", "!python -m trainer --mode inference --inference_output simple_inference.md\n"]}, {"cell_type": "code", "execution_count": 9, "id": "2e2c9721", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 07-19 09:11:12 [__init__.py:248] Automatically detected platform rocm.\n", "PyTorch detected number of available devices: 1\n", "============================================================\n", "BLOOD RELATIONS GRPO TRAINER\n", "============================================================\n", "Training Type: grpo\n", "Mode: inference\n", "Model: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Output directory: checkpoints/demo/sft\n", "Dataset file: formatted_questions_array.json\n", "Learning rate: 2e-05\n", "Epochs: 3\n", "Batch size: 1\n", "LoRA rank: 8\n", "LoRA alpha: 16\n", "Max sequence length: 512\n", "GPU IDs: 0\n", "============================================================\n", "============================================================\n", "RUNNING BATCH INFERENCE\n", "============================================================\n", "Loading questions from: test_questions_array.json\n", "Successfully loaded 10 questions from JSON file\n", "Processing 10 questions...\n", "Setting up inference model...\n", "Setting up inference model on device: cuda\n", "Loading base model: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Loading tokenizer for: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Found 1 checkpoints. Using latest: checkpoints/demo/sft/checkpoint-603\n", "Loading LoRA adapters from: checkpoints/demo/sft/checkpoint-603\n", "Inference model setup completed successfully\n", "Found 1 checkpoints. Using latest: checkpoints/demo/sft/checkpoint-603\n", "Processing question 1/10...\n", "/usr/local/lib/python3.12/dist-packages/transformers/generation/configuration_utils.py:631: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.6` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`.\n", "  warnings.warn(\n", "/usr/local/lib/python3.12/dist-packages/transformers/generation/configuration_utils.py:636: UserWarning: `do_sample` is set to `False`. However, `top_p` is set to `0.9` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `top_p`.\n", "  warnings.warn(\n", "Response generated in 22.05s. Length: 136 chars.\n", "Processing question 2/10...\n", "Response generated in 1.88s. Length: 108 chars.\n", "Processing question 3/10...\n", "Response generated in 1.94s. Length: 100 chars.\n", "Processing question 4/10...\n", "Response generated in 3.30s. Length: 246 chars.\n", "Processing question 5/10...\n", "Response generated in 1.78s. Length: 117 chars.\n", "Processing question 6/10...\n", "Response generated in 3.02s. Length: 192 chars.\n", "Processing question 7/10...\n", "Response generated in 2.30s. Length: 119 chars.\n", "Processing question 8/10...\n", "Response generated in 2.18s. Length: 161 chars.\n", "Processing question 9/10...\n", "Response generated in 2.80s. Length: 174 chars.\n", "Processing question 10/10...\n", "Response generated in 13.08s. Length: 718 chars.\n", "Batch inference complete. Results saved to sft_inference.md\n", "Answer Accuracy: 4/10 (40.00%)\n", "Format Accuracy: 10/10 (100.00%)\n", "Inference model cleaned up\n", "[rank0]:[W719 09:12:10.909903457 ProcessGroupNCCL.cpp:1476] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())\n"]}], "source": ["\n", "# Sft-trained model:\n", "!python -m trainer --mode inference --output_dir checkpoints/demo/sft --inference_output sft_inference.md\n"]}, {"cell_type": "code", "execution_count": 10, "id": "fad6a949", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 07-19 09:23:00 [__init__.py:248] Automatically detected platform rocm.\n", "PyTorch detected number of available devices: 1\n", "============================================================\n", "BLOOD RELATIONS GRPO TRAINER\n", "============================================================\n", "Training Type: grpo\n", "Mode: inference\n", "Model: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Output directory: checkpoints/demo/grpo\n", "Dataset file: formatted_questions_array.json\n", "Learning rate: 2e-05\n", "Epochs: 3\n", "Batch size: 1\n", "LoRA rank: 8\n", "LoRA alpha: 16\n", "Max sequence length: 512\n", "GPU IDs: 0\n", "============================================================\n", "============================================================\n", "RUNNING BATCH INFERENCE\n", "============================================================\n", "Loading questions from: test_questions_array.json\n", "Successfully loaded 10 questions from JSON file\n", "Processing 10 questions...\n", "Setting up inference model...\n", "Setting up inference model on device: cuda\n", "Loading base model: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Loading tokenizer for: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "Found 1 checkpoints. Using latest: checkpoints/demo/grpo/checkpoint-2010\n", "Loading LoRA adapters from: checkpoints/demo/grpo/checkpoint-2010\n", "Inference model setup completed successfully\n", "Found 1 checkpoints. Using latest: checkpoints/demo/grpo/checkpoint-2010\n", "Processing question 1/10...\n", "/usr/local/lib/python3.12/dist-packages/transformers/generation/configuration_utils.py:631: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.6` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`.\n", "  warnings.warn(\n", "/usr/local/lib/python3.12/dist-packages/transformers/generation/configuration_utils.py:636: UserWarning: `do_sample` is set to `False`. However, `top_p` is set to `0.9` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `top_p`.\n", "  warnings.warn(\n", "Response generated in 21.31s. Length: 45 chars.\n", "Processing question 2/10...\n", "Response generated in 1.02s. Length: 45 chars.\n", "Processing question 3/10...\n", "Response generated in 1.00s. Length: 45 chars.\n", "Processing question 4/10...\n", "Response generated in 1.00s. Length: 45 chars.\n", "Processing question 5/10...\n", "Response generated in 1.01s. Length: 45 chars.\n", "Processing question 6/10...\n", "Response generated in 1.00s. Length: 45 chars.\n", "Processing question 7/10...\n", "Response generated in 1.00s. Length: 45 chars.\n", "Processing question 8/10...\n", "Response generated in 1.01s. Length: 45 chars.\n", "Processing question 9/10...\n", "Response generated in 0.99s. Length: 45 chars.\n", "Processing question 10/10...\n", "Response generated in 1.01s. Length: 45 chars.\n", "Batch inference complete. Results saved to grpo_inference.md\n", "Answer Accuracy: 5/10 (50.00%)\n", "Format Accuracy: 10/10 (100.00%)\n", "Inference model cleaned up\n", "[rank0]:[W719 09:23:34.077919253 ProcessGroupNCCL.cpp:1476] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())\n"]}], "source": ["# Grpo-trained model:\n", "!python -m trainer --mode inference --output_dir checkpoints/demo/grpo --inference_output grpo_inference.md"]}, {"cell_type": "markdown", "id": "256b57f9", "metadata": {}, "source": ["\n", "#### SFT (Supervised Fine-tuning)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c68681cb", "metadata": {}, "outputs": [], "source": ["# /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "!python -m trainer \\\n", "    --training_type sft \\\n", "    --mode train \\\n", "    --model_name \"/jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\" \\\n", "    --output_dir \"checkpoints/sft\" \\\n", "    --learning_rate 2e-5 \\\n", "    --num_train_epochs 3 \\\n", "    --per_device_train_batch_size 4 \\\n", "    --lora_r 32 \\\n", "    --lora_alpha 64 \\\n", "    --disable_wandb # remove this or comment out for wandb logging"]}, {"cell_type": "markdown", "id": "0c5b5fa8", "metadata": {}, "source": ["#### RL (GRPO)"]}, {"cell_type": "code", "execution_count": null, "id": "a6b6e60f", "metadata": {}, "outputs": [], "source": ["# Train GRPO: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\n", "!python -m trainer \\\n", "    --training_type grpo \\\n", "    --mode train \\\n", "    --model_name \"/jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct\" \\\n", "    --output_dir \"checkpoints/grpo\" \\\n", "    --learning_rate 1e-5 \\\n", "    --num_train_epochs 2 \\\n", "    --per_device_train_batch_size 2 \\\n", "    --gradient_accumulation_steps 2 \\\n", "    --lora_r 16 \\\n", "    --lora_alpha 32 \\\n", "    --vllm_gpu_memory_utilization 0.7 \\\n", "    --disable_wandb # remove this or comment out for wandb logging"]}, {"cell_type": "markdown", "id": "30f99808", "metadata": {}, "source": ["**For more such use cases, go through `__main__` block of [trainer.py](trainer.py)**"]}, {"cell_type": "markdown", "id": "87bb90b4", "metadata": {}, "source": ["If SFT ckpt is used for GRPO?"]}, {"cell_type": "code", "execution_count": null, "id": "4d6748d0", "metadata": {}, "outputs": [], "source": ["# Try to bring your own code\n"]}, {"cell_type": "markdown", "id": "c747fe63", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "945b5b67", "metadata": {}, "source": ["### Prompt Tuning\n", "\n", "In this section we'll see how you efficiently communicate with your model (*make your thoughts visible*) to obtain what you desired. Just like the following image 😜."]}, {"cell_type": "markdown", "id": "38669e2a", "metadata": {}, "source": ["<img src=\"../assets/prompt.jpg\">\n", "\n", "Pic Credits: [<PERSON><PERSON><PERSON>](https://www.linkedin.com/in/eordax/)"]}, {"cell_type": "markdown", "id": "35f6ed03", "metadata": {}, "source": ["*The following prompt-tuning can be tested out at [question_agent.py](../agents/question_agent.py) `__main__` code block.*"]}, {"cell_type": "code", "execution_count": null, "id": "0bc7a7c2", "metadata": {}, "outputs": [], "source": ["# A demonstration of how tuning-prompt improves the model output.\n", "\n", "# Basic prompt:\n", "sys_prompt1 = \"You are an examiner tasked with creating extremely difficult multiple-choice questions\"\n", "\n", "output1 = \"\"\"\n", "        {\n", "            \"question\": \"Identify the next number: 23, 43, 73, 113, ?\",\n", "            \"choices\": [\n", "                \"A) 163\",\n", "                \"B) 173\",\n", "                \"C) 157\",\n", "                \"D) 167\"\n", "            ],\n", "            \"answer\": \"A) 163\",\n", "            \"explanation\": \"The differences between terms increase by 10 each time: 43-23=20, 73-43=30, 113-73=40, so next difference is 50. 113+50=163, which is also a prime.\"\n", "        }\n", "\n", "\"\"\"\n", "\n", "# After we tune the prompt as per our requirement (i.e., conveying model our intentions) we get following:\n", "sys_prompt2 = \"\"\"\n", "    You are an **expert-level examiner** with deep expertise in designing **highly challenging and conceptually rigorous multiple-choice questions (MCQs)** for the **Quantitative Aptitude and Analytical Reasoning** sections of top-tier competitive exams.\n", "    Think step by step to generate the question and solve the same, but only output the final answer. Do not show your thinking process.\n", "    **Please DO NOT reveal the solution steps or any intermediate reasoning.**\n", "\"\"\"\n", "\n", "# Output: \n", "output2 = \"\"\"\n", "        {\n", "            \"question\": \"What is the next term in the series: 2, 5, 10, 15, 90, 97, ?, 2339\",\n", "            \"choices\": [\n", "                \"A) 582\",\n", "                \"B) 1164\",\n", "                \"C) 1746\",\n", "                \"D) 2328\"\n", "            ],\n", "            \"answer\": \"D) 2328\",\n", "            \"explanation\": \"The pattern alternates between adding the next prime number and multiplying by the next factorial: 2 + 3 = 5, 5 x 2! = 10, 10 + 5 = 15, 15 x 3! = 90, 90 + 7 = 97, 97 x 4! = 2328, 2328 + 11 = 2339. So the missing term is 97 x 24 = 2328.\"\n", "        }\n", "\n", "\"\"\"\n", "\n", "# NOTE: Here the `sys_prompt` denotes the system-prompt which sets the context, tone, and boundaries for the AI's actions, shaping its overall conduct throughout the conversation"]}, {"cell_type": "markdown", "id": "a4643dfa", "metadata": {}, "source": ["##### Examples of Prompt-tuning:\n", "1. CoT\n", "2. Few-Shot (In-context) prompting\n", "3. Self-consistency decoding"]}, {"cell_type": "markdown", "id": "73e8730f", "metadata": {}, "source": ["## <span style=\"color: green\">Tips and Tricks</span>:\n", "\n", "*   SFT/GRPO/Prompt-finetuning (also Distillation) for improved response from agents. This\n", "    *   Ensure format correctness\n", "    *   Ensure question-choices-answer correctness\n", "    *   Improve question difficulty\n", "    *   Improve answer scoring\n", "    *   Try improving reasoning ability\n", "    *   Create a good training dataset (with reasoning traces maybe)\n", "*   *Datasets if required can be sourced through internet or generated using Frontier models.*\n", "*   <span style=\"color: green\">Try</span> dividing the aspects for improvements among yourselves as much as possible - *Team that works together, wins together*🏆.\n", "*   Finally, <span style=\"color : teal\">*Like catches win matches - similarly tips wins patches*</span>"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}