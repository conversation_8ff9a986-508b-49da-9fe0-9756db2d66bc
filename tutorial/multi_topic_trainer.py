import os
import torch
import argparse
import re
import json
import time
from typing import Optional, List, Dict, Any
from datasets import Dataset
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    TrainingArguments,
    TrainerCallback
)
from trl import SFTTrainer, GRPOConfig, GRPOTrainer
from peft import LoraConfig, PeftModel
import wandb

class MultiTopicTrainer:
    """Unified trainer class for both SFT and GRPO training with inference capabilities for multiple topics."""
    
    def __init__(self, args):
        """Initialize the trainer with configuration arguments."""
        self.args = args
        self.model = None
        self.tokenizer = None
        self.trainer = None
        self.dataset = None
        
        # Inference model cache
        self._inference_model = None
        self._inference_tokenizer = None
        
        # Constants
        self.reasoning_start = "<reasoning>"
        self.reasoning_end = "</reasoning>"
        self.solution_start = "<answer>"
        self.solution_end = "</answer>"
        
        # Load topic definitions
        self.topics = self._load_topics()
        
        self.system_prompt = f"""
        You are an expert in logical reasoning and complex problem-solving.
        Your task is to answer multiple-choice questions (MCQs) on various topics including:
        - Truth-teller and Liar Problems (Logical Reasoning)
        - Seating Arrangements (Linear, Circular) (Puzzles)
        - Puzzles involving generations and family tree logic (Blood Relations and Family Tree)
        
        Think about the problem and provide your working out.
        Place it between {self.reasoning_start} and {self.reasoning_end}.
        Then, provide your answer between {self.solution_start} and {self.solution_end}
        Make sure to follow the XML format strictly i.e. start with {self.reasoning_start} and end with {self.reasoning_end} for reasoning,
        and start with {self.solution_start} and end with {self.solution_end} for the answer.
        Your answer should be a single letter (A, B, C, or D) representing the correct option.
        Do not include any additional text outside of these tags.
        """
        
        # Setup environment
        self._setup_environment()
        
        # Display configuration
        self._display_config()
    
    def _load_topics(self) -> Dict[str, List[str]]:
        """Load topic definitions from topics.json file."""
        topics_file = "assets/topics.json"
        try:
            with open(topics_file, 'r', encoding='utf-8') as f:
                topics_data = json.load(f)
            print(f"Loaded {len(topics_data)} topic categories from {topics_file}")
            return topics_data
        except FileNotFoundError:
            print(f"Warning: Topics file {topics_file} not found. Using default topics.")
            return {
                "Logical Reasoning": ["Truth-teller and Liar Problems"],
                "Puzzles": ["Seating Arrangements (Linear, Circular)"],
                "Blood Relations and Family Tree": ["Puzzles involving generations and family tree logic"]
            }
        except Exception as e:
            print(f"Error loading topics: {e}. Using default topics.")
            return {
                "Logical Reasoning": ["Truth-teller and Liar Problems"],
                "Puzzles": ["Seating Arrangements (Linear, Circular)"],
                "Blood Relations and Family Tree": ["Puzzles involving generations and family tree logic"]
            }
    
    def _setup_environment(self):
        """Setup environment variables and GPU configuration."""
        # GPU selection - parse gpu_ids from args
        gpu_ids = [int(x.strip()) for x in self.args.gpu_ids.split(',') if x.strip().isdigit()]
        if not gpu_ids:
            gpu_ids = [0]  # Default to GPU 0 if no valid IDs provided
        
        os.environ.setdefault("CUDA_VISIBLE_DEVICES", ','.join(map(str, gpu_ids)))
        
        # Add environment variables for distributed training
        os.environ.setdefault("RANK", "0")
        os.environ.setdefault("LOCAL_RANK", "0")
        os.environ.setdefault("WORLD_SIZE", "1")
        os.environ.setdefault("MASTER_ADDR", "localhost")
        os.environ.setdefault("MASTER_PORT", "29500")
        
        # ROCm optimization flags for vLLM (if using GRPO)
        if self.args.training_type == 'grpo':
            os.environ.setdefault("VLLM_USE_TRITON_FLASH_ATTN", "0")
            os.environ.setdefault("VLLM_ROCM_USE_AITER", "1")
            os.environ.setdefault("VLLM_WORKER_MULTIPROC_METHOD", "spawn")
            os.environ.setdefault("SAFETENSORS_FAST_GPU", "1")
        
        print(f"PyTorch detected number of available devices: {torch.cuda.device_count()}")
    
    def _display_config(self):
        """Display training configuration."""
        print("=" * 60)
        print(f"MULTI-TOPIC {self.args.training_type.upper()} TRAINER")
        print("=" * 60)
        print(f"Training Type: {self.args.training_type}")
        print(f"Mode: {self.args.mode}")
        print(f"Model: {self.args.model_name}")
        print(f"Output directory: {self.args.output_dir}")
        print(f"Dataset file: {self.args.dataset_file}")
        print(f"Learning rate: {self.args.learning_rate}")
        print(f"Epochs: {self.args.num_train_epochs}")
        print(f"Batch size: {self.args.per_device_train_batch_size}")
        print(f"LoRA rank: {self.args.lora_r}")
        print(f"LoRA alpha: {self.args.lora_alpha}")
        print(f"Max sequence length: {self.args.max_seq_length}")
        print(f"GPU IDs: {self.args.gpu_ids}")
        
        # Display supported topics
        print(f"Supported Topics:")
        for category, subtopics in self.topics.items():
            for subtopic in subtopics:
                print(f"  - {subtopic} ({category})")
        print("=" * 60)
    
    def load_dataset(self) -> Dataset:
        """Load and process the multi-topic dataset."""
        print(f"Loading dataset from: {self.args.dataset_file}")
        
        # Load raw data once
        raw_items = self._load_raw_dataset()
        
        # Format based on training type
        if self.args.training_type == 'sft':
            self.dataset = self._format_sft_dataset(raw_items)
        else:  # grpo
            self.dataset = self._format_grpo_dataset(raw_items)
    
    def _load_raw_dataset(self) -> List[Dict[str, str]]:
        """Load raw dataset items from JSON file."""
        items = []
        
        try:
            with open(self.args.dataset_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Handle both array format and single object format
            if isinstance(data, list):
                # Array of question objects
                question_objects = data
            else:
                # Single object, wrap in list
                question_objects = [data]
            
            # Track topics found in dataset
            topics_found = set()
            
            for idx, question_obj in enumerate(question_objects, 1):
                try:
                    # Extract question from the new JSON format
                    if "question" in question_obj:
                        topic = question_obj.get("topic", "Unknown Topic")
                        topics_found.add(topic)

                        question_text = question_obj["question"]
                        
                        # Extract choices and format them as part of the question
                        choices = question_obj.get("choices", [])
                        if choices:
                            # Combine question with choices
                            full_question = f"Topic: {topic}\n" + f"Question: {question_text}\n" + "\n".join(choices)
                        else:
                            full_question = f"Topic: {topic}\n" + f"Question: {question_text}"
                        
                        # Extract answer
                        answer_letter = question_obj.get("answer", "").strip()
                        
                        # Extract explanation as reasoning
                        reasoning = question_obj.get("explanation", "Let me analyze this step by step.")
                        
                        if full_question and answer_letter:
                            items.append({
                                'question': full_question,
                                'answer': answer_letter,
                                'reasoning': reasoning,
                                'original_question': question_text,
                                'choices': choices,
                                'topic': topic
                            })
                            
                            if idx <= 3:  # Show first 3 for debugging
                                print(f"✓ Loaded question {idx} ({topic}): {question_text[:50]}...")
                        else:
                            print(f"Warning: Incomplete question data at index {idx}")
                            
                except Exception as e:
                    print(f"Warning: Error processing question at index {idx}: {e}")
                    continue
            
            # Display topic statistics
            if topics_found:
                print(f"Topics found in dataset: {sorted(topics_found)}")
                for topic in sorted(topics_found):
                    count = sum(1 for item in items if item.get('topic') == topic)
                    print(f"  - {topic}: {count} questions")

        except FileNotFoundError:
            print(f"Error: Dataset file {self.args.dataset_file} not found.")
            raise
        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON format in {self.args.dataset_file}: {e}")
            raise
        except Exception as e:
            print(f"Error: Unexpected error loading dataset: {e}")
            raise
    
        if not items:
            print("Error: No valid items found in dataset file.")
            raise ValueError("No valid training data found")
        
        print(f"Successfully loaded {len(items)} questions from JSON file")
        return items

    def _format_sft_dataset(self, raw_items: List[Dict[str, str]]) -> Dataset:
        """Format raw items for SFT training."""
        # Load tokenizer for formatting
        tokenizer = AutoTokenizer.from_pretrained(self.args.model_name, trust_remote_code=True)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        formatted_items = []

        for item in raw_items:
            question = item['question']
            answer_letter = item['answer']
            reasoning = item['reasoning']

            # Compose the model's expected output
            model_completion = (
                f"{self.reasoning_start}"
                f"{reasoning}"
                f"{self.reasoning_end}"
                f"{self.solution_start}"
                f"{answer_letter}"
                f"{self.solution_end}"
            )

            # Format as chat
            chat_messages = [
                {'role': 'system', 'content': self.system_prompt},
                {'role': 'user', 'content': question},
                {'role': 'assistant', 'content': model_completion}
            ]

            # Apply chat template
            formatted_text = tokenizer.apply_chat_template(
                chat_messages,
                tokenize=False,
                add_generation_prompt=False
            )

            formatted_items.append({
                "text": formatted_text,
                "question": question,
                "answer": model_completion
            })

        print(f"Created {len(formatted_items)} SFT training samples")
        return Dataset.from_list(formatted_items)

    def _format_grpo_dataset(self, raw_items: List[Dict[str, str]]) -> Dataset:
        """Format raw items for GRPO training."""
        formatted_items = []

        for item in raw_items:
            question = item['question']
            answer_letter = item['answer']

            # Format for GRPO
            formatted_items.append({
                'prompt': [
                    {'role': 'system', 'content': self.system_prompt},
                    {'role': 'user', 'content': question}
                ],
                'answer': answer_letter
            })

        print(f"Created {len(formatted_items)} GRPO training samples")
        return Dataset.from_list(formatted_items)

    def setup_model_and_tokenizer(self):
        """Initialize model and tokenizer."""
        print(f"Loading model: {self.args.model_name}")

        self.model = AutoModelForCausalLM.from_pretrained(
            self.args.model_name,
            torch_dtype=torch.bfloat16,
            device_map=None,
            trust_remote_code=True,
        )

        if torch.cuda.is_available():
            self.model = self.model.to("cuda")

        # Setup model configuration
        self.model.config.use_cache = False
        if hasattr(self.model.config, 'pretraining_tp'):
            self.model.config.pretraining_tp = 1

        # Setup tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.args.model_name,
            trust_remote_code=True,
            use_fast=True
        )

        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        self.tokenizer.padding_side = "right"
        self.tokenizer.model_max_length = self.args.max_seq_length

        print("Model and tokenizer loaded successfully")

    def setup_peft_config(self) -> LoraConfig:
        """Setup LoRA configuration."""
        return LoraConfig(
            r=self.args.lora_r,
            lora_alpha=self.args.lora_alpha,
            lora_dropout=self.args.lora_dropout,
            bias="none",
            task_type="CAUSAL_LM",
            target_modules="all-linear"
        )

    def setup_wandb(self):
        """Initialize Weights & Biases logging."""
        if not self.args.disable_wandb:
            try:
                wandb.init(
                    project=self.args.wandb_project,
                    name=self.args.wandb_run_name,
                    config={
                        "training_type": self.args.training_type,
                        "model_name": self.args.model_name,
                        "learning_rate": self.args.learning_rate,
                        "num_train_epochs": self.args.num_train_epochs,
                        "per_device_train_batch_size": self.args.per_device_train_batch_size,
                        "gradient_accumulation_steps": self.args.gradient_accumulation_steps,
                        "lora_r": self.args.lora_r,
                        "lora_alpha": self.args.lora_alpha,
                        "lora_dropout": self.args.lora_dropout,
                        "max_seq_length": self.args.max_seq_length,
                        "dataset_file": self.args.dataset_file,
                        "gpu_ids": self.args.gpu_ids,
                    }
                )
                print("Weights & Biases initialized successfully")
            except Exception as e:
                print(f"WandB initialization failed: {e}. Training will continue without WandB.")
                self.args.disable_wandb = True

    def train_sft(self):
        """Train using Supervised Fine-Tuning."""
        print("Starting SFT training...")

        # Setup training arguments
        training_args = TrainingArguments(
            output_dir=self.args.output_dir,
            num_train_epochs=self.args.num_train_epochs,
            per_device_train_batch_size=self.args.per_device_train_batch_size,
            gradient_accumulation_steps=self.args.gradient_accumulation_steps,
            save_strategy="steps",
            save_steps=100,
            logging_steps=10,
            learning_rate=self.args.learning_rate,
            weight_decay=0.01,
            fp16=False,
            bf16=True,
            max_grad_norm=0.3,
            max_steps=-1,
            warmup_ratio=0.03,
            group_by_length=True,
            lr_scheduler_type="cosine",
            report_to="wandb" if not self.args.disable_wandb else "none",
        )

        # Setup LoRA config
        peft_config = self.setup_peft_config()

        # Create trainer
        self.trainer = SFTTrainer(
            model=self.model,
            processing_class=self.tokenizer,  # Changed from 'tokenizer' to 'processing_class'
            args=training_args,
            train_dataset=self.dataset,
            peft_config=peft_config,
        )

        # Start training
        self.trainer.train()

        # Save model
        print(f"Saving LoRA adapters to {self.args.output_dir}")
        self.trainer.save_model(self.args.output_dir)
        self.tokenizer.save_pretrained(self.args.output_dir)

        print("SFT training completed successfully")

    def train(self):
        """Main training function that handles both SFT and GRPO."""
        print(f"Starting {self.args.training_type.upper()} training...")

        # Load dataset
        self.load_dataset()

        # Setup model and tokenizer
        self.setup_model_and_tokenizer()

        # Setup wandb
        self.setup_wandb()

        # Train based on type
        if self.args.training_type == 'sft':
            self.train_sft()
        else:  # grpo
            print("GRPO training not implemented in this simplified version")
            print("Please use the full trainer.py for GRPO training")

        # Cleanup
        if not self.args.disable_wandb and wandb.run:
            wandb.finish()

        print(f"{self.args.training_type.upper()} training completed!")


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Multi-Topic Trainer for Logical Reasoning, Puzzles, and Blood Relations")

    # General arguments
    parser.add_argument("--model_name", type=str, default="/jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct", help="Model name or path")
    parser.add_argument("--output_dir", type=str, default="checkpoints/demo/multi_topic_sft", help="Output directory for checkpoints and model")
    parser.add_argument("--dataset_file", type=str, default="multi_topic_questions.json", help="Path to the dataset file (JSON)")
    parser.add_argument("--gpu_ids", type=str, default="0", help="GPU IDs to use (comma-separated)")

    # Training arguments
    parser.add_argument("--training_type", type=str, choices=["sft"], default="sft", help="Type of training (simplified version only supports SFT)")
    parser.add_argument("--mode", type=str, choices=["train"], default="train", help="Mode of operation (simplified version only supports training)")
    parser.add_argument("--num_train_epochs", type=int, default=3, help="Number of training epochs")
    parser.add_argument("--per_device_train_batch_size", type=int, default=1, help="Batch size per device during training")
    parser.add_argument("--gradient_accumulation_steps", type=int, default=1, help="Number of gradient accumulation steps")
    parser.add_argument("--learning_rate", type=float, default=2e-5, help="Learning rate")
    parser.add_argument("--lora_r", type=int, default=8, help="LoRA rank")
    parser.add_argument("--lora_alpha", type=float, default=16, help="LoRA alpha")
    parser.add_argument("--lora_dropout", type=float, default=0.1, help="LoRA dropout rate")
    parser.add_argument("--max_seq_length", type=int, default=512, help="Maximum sequence length")

    # WandB arguments
    parser.add_argument("--disable_wandb", action="store_true", help="Disable Weights & Biases logging")
    parser.add_argument("--wandb_project", type=str, default="multi_topic_reasoning", help="WandB project name")
    parser.add_argument("--wandb_run_name", type=str, help="WandB run name")

    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()

    # Auto-generate wandb run name if not provided
    model_display_name = args.model_name.split('/')[-1] if '/' in args.model_name else args.model_name
    if args.wandb_run_name is None:
        args.wandb_run_name = f"{model_display_name}-{args.training_type}-r{args.lora_r}-lr{args.learning_rate}"

    # Create trainer
    trainer = MultiTopicTrainer(args)

    try:
        # Run training
        trainer.train()

    except Exception as e:
        print(f"Error during processing: {e}")

    finally:
        # Cleanup
        if not args.disable_wandb and wandb.run:
            wandb.finish()


if __name__ == "__main__":
    """
    MULTI-TOPIC UNIFIED TRAINER
    ============================

    This script provides a unified interface for training multi-topic reasoning models
    using Supervised Fine-Tuning (SFT). It supports three main topic categories:
    - Truth-teller and Liar Problems (Logical Reasoning)
    - Seating Arrangements (Linear, Circular) (Puzzles)
    - Puzzles involving generations and family tree logic (Blood Relations and Family Tree)

    USAGE EXAMPLE:

    python multi_topic_trainer.py \
        --training_type sft \
        --model_name /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct \
        --output_dir checkpoints/demo/multi_topic_sft \
        --dataset_file multi_topic_questions.json \
        --learning_rate 2e-5 \
        --num_train_epochs 3 \
        --per_device_train_batch_size 4 \
        --lora_r 32 \
        --lora_alpha 64
    """
    main()
