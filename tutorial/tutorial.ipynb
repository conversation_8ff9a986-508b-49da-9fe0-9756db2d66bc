!pwd

# Uncomment and execute if not done in README.ipynb
!pip install -r ../default_requirements.txt

# Change directory if not in tutorial
import os
#os.chdir("./tutorial")
!pwd

# os.environ['HF_TOKEN'] = ""
# os.environ['HF_HOME']=""

# Base model:
!python -m trainer --mode inference --inference_output simple_inference.md



# Sft-trained model:
!python -m trainer --mode inference --output_dir checkpoints/demo/sft --inference_output sft_inference.md


# Grpo-trained model:
!python -m trainer --mode inference --output_dir checkpoints/demo/grpo --inference_output grpo_inference.md

# /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct
!python -m trainer \
    --training_type sft \
    --mode train \
    --model_name "/jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct" \
    --output_dir "checkpoints/sft" \
    --learning_rate 2e-5 \
    --num_train_epochs 3 \
    --per_device_train_batch_size 4 \
    --lora_r 32 \
    --lora_alpha 64 \
    --disable_wandb # remove this or comment out for wandb logging

# Train GRPO: /jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct
!python -m trainer \
    --training_type grpo \
    --mode train \
    --model_name "/jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct" \
    --output_dir "checkpoints/grpo" \
    --learning_rate 1e-5 \
    --num_train_epochs 2 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 2 \
    --lora_r 16 \
    --lora_alpha 32 \
    --vllm_gpu_memory_utilization 0.7 \
    --disable_wandb # remove this or comment out for wandb logging

# Try to bring your own code


# A demonstration of how tuning-prompt improves the model output.

# Basic prompt:
sys_prompt1 = "You are an examiner tasked with creating extremely difficult multiple-choice questions"

output1 = """
        {
            "question": "Identify the next number: 23, 43, 73, 113, ?",
            "choices": [
                "A) 163",
                "B) 173",
                "C) 157",
                "D) 167"
            ],
            "answer": "A) 163",
            "explanation": "The differences between terms increase by 10 each time: 43-23=20, 73-43=30, 113-73=40, so next difference is 50. 113+50=163, which is also a prime."
        }

"""

# After we tune the prompt as per our requirement (i.e., conveying model our intentions) we get following:
sys_prompt2 = """
    You are an **expert-level examiner** with deep expertise in designing **highly challenging and conceptually rigorous multiple-choice questions (MCQs)** for the **Quantitative Aptitude and Analytical Reasoning** sections of top-tier competitive exams.
    Think step by step to generate the question and solve the same, but only output the final answer. Do not show your thinking process.
    **Please DO NOT reveal the solution steps or any intermediate reasoning.**
"""

# Output: 
output2 = """
        {
            "question": "What is the next term in the series: 2, 5, 10, 15, 90, 97, ?, 2339",
            "choices": [
                "A) 582",
                "B) 1164",
                "C) 1746",
                "D) 2328"
            ],
            "answer": "D) 2328",
            "explanation": "The pattern alternates between adding the next prime number and multiplying by the next factorial: 2 + 3 = 5, 5 x 2! = 10, 10 + 5 = 15, 15 x 3! = 90, 90 + 7 = 97, 97 x 4! = 2328, 2328 + 11 = 2339. So the missing term is 97 x 24 = 2328."
        }

"""

# NOTE: Here the `sys_prompt` denotes the system-prompt which sets the context, tone, and boundaries for the AI's actions, shaping its overall conduct throughout the conversation