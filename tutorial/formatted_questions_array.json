[{"topic": "Puzzles involving generations and family tree logic", "question": "A is <PERSON>'s mother. <PERSON> is <PERSON>'s only son. <PERSON> is <PERSON>'s husband. <PERSON>'s only sibling, <PERSON>, is unmarried. <PERSON>'s father, <PERSON>, has only one son. <PERSON> is <PERSON>'s wife. If <PERSON> has no siblings, how is <PERSON>'s son-in-law related to <PERSON>?", "choices": ["A) Husband", "B) Son", "C) Brother", "D) Father"], "answer": "A", "explanation": "**A is <PERSON>'s mother, and <PERSON> is <PERSON>'s only son. ** This implies that <PERSON> and <PERSON> are <PERSON>'s parents."}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON>'s father, <PERSON>, is the only son of <PERSON>'s husband. <PERSON> has only one child. <PERSON> is <PERSON>'s daughter-in-law. <PERSON> has an only child, <PERSON>. If <PERSON> is male and T is female, how is <PERSON>'s wife related to <PERSON>'s paternal grandmother?", "choices": ["A) Daughter", "B) <PERSON><PERSON><PERSON>", "C) Granddaughter-in-law", "D) Sister-in-law"], "answer": "C", "explanation": "**Identify key individuals and their primary relationships:**\n    *   \"<PERSON>'s father, <PERSON>, is the only son of <PERSON>'s husband. \" This means <PERSON> is the son of <PERSON>'s husband."}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is married to <PERSON><PERSON>'s brother, <PERSON>, is the only son of his parents, <PERSON> and <PERSON><PERSON>'s only daughter, <PERSON>, has a son <PERSON><PERSON> <PERSON>'s maternal uncle is <PERSON><PERSON> <PERSON> is married to <PERSON>. If <PERSON> is the mother of <PERSON>'s only nephew (from <PERSON>'s sibling's side), how is <PERSON> related to <PERSON>?", "choices": ["A) Daughter", "B) Daughter-in-law", "C) Sister", "D) <PERSON><PERSON><PERSON>"], "answer": "B", "explanation": "**Identify family members N, O, <PERSON>, <PERSON>, L:**\n    *   <PERSON> and <PERSON> are parents. *   <PERSON> is their only son (<PERSON> is male)."}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON>'s father, <PERSON>, is <PERSON>'s maternal grandfather. <PERSON>'s mother, <PERSON>, is <PERSON>'s only daughter. <PERSON>'s husband, <PERSON>, has a mother <PERSON>, and <PERSON>'s only other child (besides <PERSON>) is <PERSON> (<PERSON>'s sister). If <PERSON> is male, how is <PERSON>'s sister related to <PERSON>?", "choices": ["A) Sister", "B) Sister-in-law", "C) Cousin", "D) Aunt"], "answer": "B", "explanation": "\" So, <PERSON> is <PERSON>'s mother. *   Therefore, <PERSON> is <PERSON>'s father."}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON>'s son, <PERSON>, is married to <PERSON><PERSON> <PERSON>'s father, <PERSON>, is the only brother of <PERSON>. <PERSON> has only one child, <PERSON><PERSON>'s paternal grandmother is <PERSON><PERSON>. <PERSON><PERSON>'s husband, <PERSON><PERSON>, has only one daughter-in-law (who is <PERSON>). If <PERSON> has no siblings, how is <PERSON> related to <PERSON><PERSON>?", "choices": ["A) Sister-in-law of <PERSON><PERSON>'s son", "B) Mother of <PERSON><PERSON>'s daughter-in-law's niece's husband", "C) No direct kinship relation", "D) <PERSON><PERSON>'s wife's co-sister"], "answer": "B", "explanation": "Let's break down the relationships step by step:\n1. **<PERSON>'s son, <PERSON>, is married to <PERSON>."}, {"topic": "Puzzles involving generations and family tree logic", "question": "A is father of <PERSON> (male). <PERSON> is spouse of <PERSON><PERSON> <PERSON>'s only sibling is <PERSON><PERSON> <PERSON>'s mother, <PERSON>, is married to <PERSON><PERSON> <PERSON> has only one son-in-law (who is <PERSON>). <PERSON> is <PERSON>'s granddaughter, and <PERSON>'s mother is <PERSON>. How is <PERSON> related to <PERSON>?", "choices": ["A) Brother", "B) <PERSON>'s son-in-law", "C) Father-in-law of <PERSON>'s daughter", "D) Grandfather of <PERSON>'s daughter's child"], "answer": "C", "explanation": "(A --father--> B)\n    *   B is the spouse of <PERSON>. Since B is male, C is female."}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON>'s only paternal aunt, <PERSON>, is married to <PERSON><PERSON> <PERSON>'s father-in-law is <PERSON>. <PERSON> has only one son, <PERSON><PERSON> <PERSON>'s wife, <PERSON>, has a brother, <PERSON><PERSON> <PERSON>'s mother is <PERSON>. If <PERSON> is male, how is <PERSON> related to <PERSON>'s father?", "choices": ["A) Mother", "B) Sister", "C) Mother-in-law", "D) Aunt"], "answer": "C", "explanation": "<PERSON>'s father-in-law is <PERSON>. This means <PERSON> is <PERSON>'s father."}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON> is the brother of <PERSON>'s husband's only sibling. <PERSON>'s father-in-law, <PERSON>, has only one daughter, <PERSON><PERSON> <PERSON>'s husband is <PERSON><PERSON> <PERSON>'s son is <PERSON>. If <PERSON>'s mother is <PERSON>'s wife, how is <PERSON> related to <PERSON>?", "choices": ["A) Son", "B) Nephew", "C) Cousin", "D) Brother-in-law"], "answer": "B", "explanation": "Let <PERSON>'s husband be <PERSON><PERSON>. <PERSON> is <PERSON>'s father-in-law, so <PERSON> is <PERSON><PERSON>'s father."}, {"topic": "Puzzles involving generations and family tree logic", "question": "<PERSON>'s spouse is <PERSON><PERSON> <PERSON>'s only sibling, <PERSON> (male), is married to <PERSON><PERSON> <PERSON>'s mother, <PERSON>, is the wife of <PERSON><PERSON> <PERSON> has only one son-in-law, <PERSON> (who is <PERSON>'s husband). <PERSON>'s grandson is <PERSON>, and <PERSON>'s father is <PERSON>. How is <PERSON> related to <PERSON>?", "choices": ["A) Daughter", "B) Sister", "C) Daughter-in-law", "D) <PERSON><PERSON><PERSON>"], "answer": "C", "explanation": "**<PERSON>'s only sibling, <PERSON> (male), is married to <PERSON>:**\n    *   <PERSON> and <PERSON> are siblings. *   C is male."}, {"topic": "Puzzles involving generations and family tree logic", "question": "A is <PERSON>'s maternal uncle. <PERSON>'s mother, <PERSON>, is the only daughter of <PERSON> (<PERSON>'s parent). <PERSON>'s only son is <PERSON><PERSON> <PERSON>'s wife is <PERSON><PERSON> <PERSON>'s son is <PERSON>. <PERSON> is married to <PERSON>. How is <PERSON> related to <PERSON>'s paternal grandmother?", "choices": ["A) Sister", "B) Daughter-in-law", "C) Mother", "D) Aunt"], "answer": "B", "explanation": "**Identify A and E:**\n    *   <PERSON> is <PERSON>'s maternal uncle. <PERSON>'s mother is <PERSON>."}]