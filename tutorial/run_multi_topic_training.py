#!/usr/bin/env python3
"""
Multi-Topic Training Script
===========================

This script demonstrates how to train a model on all 3 topics:
1. Truth-teller and Liar Problems (Logical Reasoning)
2. Seating Arrangements (Linear, Circular) (Puzzles)
3. Puzzles involving generations and family tree logic (Blood Relations and Family Tree)

Usage:
    python run_multi_topic_training.py

This will train a model using the multi_topic_trainer.py script with the
multi_topic_questions.json dataset that contains questions from all 3 topics.
"""

import subprocess
import sys
import os

def run_training():
    """Run the multi-topic training."""
    
    print("=" * 60)
    print("MULTI-TOPIC TRAINING SCRIPT")
    print("=" * 60)
    print("This script will train a model on all 3 topics:")
    print("1. Truth-teller and Liar Problems (Logical Reasoning)")
    print("2. Seating Arrangements (Linear, Circular) (Puzzles)")
    print("3. Puzzles involving generations and family tree logic (Blood Relations)")
    print("=" * 60)
    
    # Check if the dataset file exists
    dataset_file = "tutorial/multi_topic_questions.json"
    if not os.path.exists(dataset_file):
        print(f"Error: Dataset file {dataset_file} not found!")
        print("Please make sure the multi_topic_questions.json file exists.")
        return False
    
    # Check if the trainer script exists
    trainer_script = "tutorial/multi_topic_trainer.py"
    if not os.path.exists(trainer_script):
        print(f"Error: Trainer script {trainer_script} not found!")
        print("Please make sure the multi_topic_trainer.py file exists.")
        return False
    
    # Training command
    cmd = [
        sys.executable, trainer_script,
        "--training_type", "sft",
        "--model_name", "/jupyter-tutorial/hf_models/Llama-3.2-1B-Instruct",
        "--output_dir", "checkpoints/demo/multi_topic_sft",
        "--dataset_file", dataset_file,
        "--learning_rate", "2e-5",
        "--num_train_epochs", "3",
        "--per_device_train_batch_size", "2",
        "--lora_r", "16",
        "--lora_alpha", "32",
        "--max_seq_length", "512",
        "--gpu_ids", "0"
    ]
    
    print("Running training command:")
    print(" ".join(cmd))
    print("=" * 60)
    
    try:
        # Run the training
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("=" * 60)
        print("✅ Multi-topic training completed successfully!")
        print("✅ Model trained on all 3 topics:")
        print("   - Truth-teller and Liar Problems")
        print("   - Seating Arrangements (Linear, Circular)")
        print("   - Blood Relations and Family Tree")
        print("=" * 60)
        return True
        
    except subprocess.CalledProcessError as e:
        print("=" * 60)
        print(f"❌ Training failed with error code: {e.returncode}")
        print("Please check the error messages above.")
        print("=" * 60)
        return False
    except KeyboardInterrupt:
        print("=" * 60)
        print("⚠️  Training interrupted by user.")
        print("=" * 60)
        return False
    except Exception as e:
        print("=" * 60)
        print(f"❌ Unexpected error: {e}")
        print("=" * 60)
        return False

def main():
    """Main function."""
    print("Starting multi-topic training...")
    
    success = run_training()
    
    if success:
        print("\n🎉 Training completed successfully!")
        print("\nNext steps:")
        print("1. Check the output directory: checkpoints/demo/multi_topic_sft")
        print("2. Use the trained model for inference on any of the 3 topics")
        print("3. Compare performance with the single-topic Blood Relations trainer")
    else:
        print("\n❌ Training failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
