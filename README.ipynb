{"cells": [{"cell_type": "markdown", "id": "480066b8", "metadata": {}, "source": ["<h1 align='center'>Welcome to the AMD AI Premier League (AAIPL)!</h1>\n"]}, {"cell_type": "markdown", "id": "e3ea12fc", "metadata": {}, "source": ["<!-- <img src=\"./assets/aaipl.png\"> -->\n", "<img src=\"./assets/AMDAAIPL.png\">"]}, {"cell_type": "markdown", "id": "09b4b3fe", "metadata": {}, "source": ["\n", "---\n", "## Task:\n", "Here you will be building:\n", "1.  A question agent or Q-agent (e.g., [question_model.py](./agents/question_model.py) & [question_agent.py](./agents/question_agent.py)) that will ask some $N$ puzzle-based questions based on some given [topics](./assets/topics.json). *Note your question agent should output questions in the format specified in [sample_question.json](./assets/sample_question.json)*.\n", "2.  Also, an answer agent or A-agent (e.g., [answer_model.py](./agents/answer_model.py) & [answer_agent.py](./agents/answer_agent.py)) that answer questions asked from question agent. Here too, the format of the output should follow as specified in [sample_answer.json](./assets/sample_answer.json) file.\n", "\n", "---\n"]}, {"cell_type": "markdown", "id": "5ed3cfa0", "metadata": {}, "source": ["## Instructions\n", "\n", "1.  How to initiate your work station?\n", "    -   Type `http://xxx.xxx.xxx.xxx:8080` link (in *Chrome*), where `xxx.xxx.xxx.xxx` is the 12 (or 11) digit IP shared with each team. Hiting this URL, will open up a Jupyter lab page. Enter the token as `amdhack` (same for all).\n", "    -   Upon landing into Jupyter Lab page, on left side (i.e., folders), you will see `AAIPL/`, `hf_models/`. All this will be inside `/jupyter-tutorial` directory.\n", "    -   Within `hf_models/` there are some base checkpoints which will be used for question and answer agent creation. For both, Q-agent and A-agent, we are using `Qwen3-4B` (with `enable_thinking=False` to avoid thinking tokens) as base model.\n", "    -   Note that inside `AAIPL/`, there is a `tutorial` folder which consists of python scripts and `.ipynb` file demonstrating how to do *SFT*, *GRPO*, and *Prompt-tuning*. You are encouraged to, of course, improve/edit upon this (BYOC). No need to stick with this strictly.\n", "2.  You got 24 hrs to bake this cake!\n", "3.  All the BEST!"]}, {"cell_type": "markdown", "id": "3cfd0427", "metadata": {}, "source": ["## 📚 Table of Contents:\n", "- 🏁 [Welcome to the AMD AI Premier League (AAIPL)!](#welcome-to-the-amd-ai-premier-league-aaipl)\n", "- 📝 [Task](#task)\n", "- ⚙️ [Instructions](#instructions)\n", "- 🏏 [Tournament Overview](#tournament-overview)\n", "- 📋 [Guidelines](#guidelines)\n", "    - [Naming Conventions](#naming-conventions)\n", "    - [Format Overview](#format-overview)\n", "- 🛠️ [What you will submit?](#️what-you-will-submit)\n", "- ⚠️ [RESTRICTIONS](#restrictions)\n", "    - [ALLOWED](#allowed)\n", "- 📂 [Directory & Files overview](#directory--files-overview)\n", "- 🚀 [Env Setup](#env-setup)\n", "- 🎮 [Let the GAME begin!!!](#let-the-game-begin)\n", "    - 🤔 [Q-Agent](#q-agent)\n", "        - ✅ [Basic format-checks for questions from Q-agent](#basic-format-checks-for-questions-from-q-agent)\n", "    - 🤖 [A-agent](#a-agent)\n", "        - ✅ [Basic format-checks for answers from A-agent](#basic-format-checks-for-answers-from-a-agent)\n", "- 🏅 [Evaluation](#evaluation)\n", "    - 📊 [Sc<PERSON> Criteria](#scoring-criteria)\n", "- ⏱ [Time Limit](#time-limit)\n", "<!-- - 🏆 [LeaderBoard UI/UX](#leaderboard-uiux) -->"]}, {"cell_type": "markdown", "id": "9386cb37", "metadata": {}, "source": ["## Tournament Overview\n", "<!-- 🏏  -->\n", "1.  All matches in this tournament will be **1v1** knockout format where two teams, Team-A vs Team-B, will compete with their Q-agent (question agent) and A-agent (answer agent). For simplicity think Q-agent to be bowler and A-agent to be batsman.\n", "2.  Like a cricket match, this would also have two innings:\n", "\n", "    -   1st inning:\n", "        *   $N$ Question from the Q-agent (Team-A) and their corresponding $N$ answers from the A-agent (Team-B).\n", "        *   Q-agent score (Team-A): Say, $40$\n", "        *   A-agent score (Team-B): $60$\n", "\n", "    -   2nd inning:\n", "        *   $N$ Question from the Q-agent (Team-B) and their respective $N$ responses from the A-agent (Team-A).\n", "        *   Q-agent score (Team-B): Say, $70$\n", "        *   A-agent score (Team-A): $30$\n", "    -   Final Score:\n", "        *   Team-A score $=$ 1st inning Q-agent score $+$ 2nd inning A-agent score $= 40 + 30 = 70$\n", "        *   Team-B score $=$ 1st inning Q-agent score $+$ 2nd inning A-agent score $= 60 + 70 = 130$\n", "\n", "    -   Winner: **Team-B** with a score of $130$.\n", "    -   For more info on <b> how SCORING is done</b>, kindly refer to this [cell](#scoring-criteria).\n", "\n", "<u>NOTE</u>: In case of **TIE**, we will use some (closed) benchmark questions, we will evaluate your answer agents (A-agent) and rank the teams accordingly.\n", "\n", "**Whichever Team's Q-agent fails to generate atleast $50\\%$ of `num_questions` (where `num_questions` ranges from $2$ to $1000+$) of the questions correctly (as per [format-checking](#format-overview)) will be automatically disqualified.**<br>\n", "<u>Note</u>: Here $N$ denotes the number of filtered / format-correct questions.\n"]}, {"cell_type": "markdown", "id": "2deab9cf", "metadata": {}, "source": ["\n", "## Guidelines:\n", "<!-- 📋  -->\n", "#### Naming Conventions:\n", "<ol type=\"a\">\n", "    <li>Rename this whole folder as <code>AAIPL_your_IP</code> if not done already. This <code>your_IP</code> will be <code>_</code> separated IPv4 address, no special-characters allowed. Follow the below <a href=\"#what-you-will-submit\">cell</a> for more info</li>\n", "    <li> For Q-agent:\n", "        <ol type=\"i\">\n", "            <li>For Q-agent wrapper <code>.py</code> file: <code>agents/question_agent.py</code>.</li>\n", "            <li>For Q-agent model <code>.py</code> file: <code>agents/question_model.py</code>.</li>\n", "        </ol>\n", "    </li>\n", "    <li> For A-agent:\n", "        <ol type=\"i\">\n", "            <li>For A-agent wrapper <code>.py</code> file: <code>agents/answer_agent.py</code>.</li>\n", "            <li>For A-agent model <code>.py</code> file: <code>agents/answer_model.py</code>.</li>\n", "        </ol>\n", "    </li>\n", "</ol>\n", "\n", "\n", "#### Format Overview\n", "-   <u>Q-Agent</u>: Given a topic, the Q-agent should generate questions in the specified JSON format:\n", "    ```json\n", "    {\n", "    \"topic\": \"<Topic of the Question>\",\n", "    \"question\": \"<full question text>\",\n", "    \"choices\": [\n", "        \"A) <choice A text>\",\n", "        \"B) <choice B text>\",\n", "        \"C) <choice C text>\",\n", "        \"D) <choice D text>\"\n", "    ],\n", "    \"answer\": \"<correct choice letter only>\",\n", "    \"explanation\": \"brief explanation within 100 words for why the answer is correct\"\n", "    }\n", "    ```\n", "    from which we will extract **ONLY** the **\"Question\"** and **\"Choices\"** keys and feed it to the answer agent. The **\"Topic\"**, **\"Question\"**, **\"Choices\"**, and **\"Answer\"** will be verified for correctness from an Oracle.\n", "-   <u>A-agent</u>: Given a Question and Choices, A-agent should produce answer in the format of:\n", "    ```json\n", "    {\n", "        \"answer\": \"<correct choice letter only>\",\n", "        \"reasoning\": \"brief reasoning within 100 words for why the answer is correct\"\n", "    }\n", "    ```\n", "    where we will extract <PERSON><PERSON><PERSON> the **\"Answer\"** key and compare it with **\"Answer\"** from the opponent's question.\n", "-   *<u>Remarks</u>: Having explanation and reasoning is a plus. Not having them doesn't disqualify the question or answer being correct.*\n", "    \n", "**<u>Note</u>**: *We will only consider those responses from the Q-agent and the A-agent which follow the above format.*\n"]}, {"cell_type": "markdown", "id": "b858a803", "metadata": {}, "source": ["## What you will submit?\n", "<!-- 🛠️  -->\n", "You need to submit your code which should contain these main files:\n", "\n", "<!-- *   `q_agent.py` (with one arg as `num_quetions: int`) - On running with `num_questions=20` should generate 20 questions in the required format as above.\n", "*   `a_agent.py` (with two args as `Question: str` and `Choices: List[str]`) - On running with the above two args should produce the o/p in the required format as above. -->\n", "<!-- 1. Submit the whole folder AAIPL with its name modified to `AAIP_<your_team_name_in_alphanumeric>`. No special characters, e.g., `#`, `@`, `!`, etc. are allowed in the team name. \n", "   - Example: `AAIP_Team1` or `AAIP_Team23` or `AAIP_Win47` are valid, but `AAIP_Team#1` or `AAIP_Team@1` are not.\n", "2. Also put Checkpoints (e.g., `model.safetensors` or `.pt` or `.pth`) file (situated at e.g., `ckpts/`) - given that they get successfully loaded automatically, when we execute inference as done above for both, question and answer agent.\n", "3. `requirements.txt` - This file lists all the extra dependencies required to run your agents apart from `default_requirements.txt`. -->\n", "\n", "1. Rename the `AAIPL` folder to `<PERSON>IPL_<your_IP_address>` if not done already. NO special characters, e.g., `#`, `@`, `!`, etc. are allowed except underscore, `_`, in the team name. \n", "   - Example: `AAIPL_192_154_162_143` or `AAIPL_192_154_182_14` are valid, but `AAIPL_Team#1` or `AAIPL_Team@1` are not. \n", "1. **No need to upload anything to anywhere, we'll collect your codes at sharp 2:00 PM - 13th July, 2025 from your Jupyter Server.**\n", "2. <span style=\"color: red;\">Don't forget to add your PPT (in `solution.pdf` format) summarizing the techniques you adopted to execute this task better, relative to this file (i.e., just inside `AAIPL_xxx_xxx_xxx_xxx` folder).</span>\n", "3. **ENSURE model checkpoint(s) (e.g., `model.safetensors` or `.pt` or `.pth`) is(are) loading and expected files are getting generated from Q-agent and A-agent, when inference is done. And put all your checkpoints in the `ckpt/` folder, located just inside `AAIPL_<your_IP>/`.**\n", "4. **<u>NOTE</u>: You are not required to generate any `.json` for us, we'll do that for you during evaluation setting a specific value to $N$.**\n", "\n", "<u><span style=\"color: blue\">NOTE</span></u>: These files will be checked for any hardcoding, RAG, or other unfair practices.<br>\n", "<u><span style=\"color: red\">REMARKS / CAUTION</span></u>: A-agent is equally important as Q-agent. So, please do focus on both."]}, {"cell_type": "markdown", "id": "fe1cc2ce", "metadata": {}, "source": ["## RESTRICTIONS\n", "<!-- ⚠️ -->\n", "\n", "1.  Kindly don't use any sort of ***RAG (Retrieval Augmented Generation)*** techniques. If found, the submission won't be considered for further evaluations.\n", "2.  **Usage of base models other than what given for Question (i.e., `Qwen3-4B`) and Answer (i.e., again `Qwen3-4B`) agent, will lead to disqualification.**\n", "3.  Do follow the guidelines as mentioned in [What you will submit?](#what-you-will-submit) section.\n", "4.  **<span style=\"color: red\">NO</span> LAST Minute Submission**: The submission deadline is strict. Upload link will expires just one minute before the deadline. So, please make sure you submit your code well in advance.\n", "5.  Any **<span style=\"color: red\">HACKEY</span>** approach or **hard-coding** will lead to disqualification.\n", "    -   E.g., Any hard-coded *adversarial attacks* that make A-agent hallucinates.\n", "6.  **Language Restriction**: ONLY English language is allowed for both Q-agent and A-agent. Any other language will lead to disqualification.\n", "7.  Strictly stay within the `max_tokens` limit as specified in `agen.yaml` & `qgen.yaml`. While other parameters can be changed as per your convenience.\n", "8.  $N$ should be passed as an argument to `question_agent.py`. We'll test for $N=1$. `--num_questions` is the argument.\n", "9.  Ensure **$40\\%$** of the questions you generate gets filtered into `questions.json`.\n", "\n", "\n", "### ALLOWED\n", "<!-- ✅  -->\n", "1.  Participants are encouraged to modify the code scripts (for any sort of training, data construction, inference, such that the above constraints are not overruled).\n", "2.  If you want to add `WANDB_API_KEY` for `wandb` logging do it in add `WANDB_API_KEY=xxxxxxx` before `python -m <script>.py` command. E.g., `!WANDB_API_KEY=xxxxxxx python -m agents.question_agent \\`\n"]}, {"cell_type": "markdown", "id": "f0f34ed1", "metadata": {}, "source": ["## Token & Time Limit:\n", "<!-- ⏱  -->\n", "*   Maximum length (e.g., `max_token`) limit for your model response should be within following tokens.\n", "    *   For question-agent (Q-Agent) it is $100$ tokens cumulatively for the content corresponding to [`topic`, `question`, `choices`, and `answer`]. This excludes token count for double quotes as well as string length for topic, question, choices, and answer string it\n", "    *   And the rest is for explanation i.e., $1024-100 = 924$. But within time limit\n", "*   `ckpt` is the folder 📂 which you will place under `AAIPL_XXX_XXX_XXX_XXX`. While `checkpoints` folder 📂 inside `tutorial/` is meant for tutorial.\n", "*   Each question should be generated under `10 secs`. Overall time for 100 questions should be no more than `1000 secs` ~ `17 mins`.\n", "*   Each answer should be generated under `6 secs`. Overall time for 100 answers should be no more than `600 secs` ~ `10 mins`.\n", "*   *Note: We will only consider those questions' and answers' JSON file that remain under the time limit.*"]}, {"cell_type": "markdown", "id": "7b562cb4", "metadata": {}, "source": ["### Directory & Files overview\n", "<!-- 📂  -->\n", "```\n", ".\n", "├── agents\n", "│   ├── question_model.py\n", "│   ├── question_agent.py\n", "│   ├── answer_model.py\n", "│   └── answer_agent.py\n", "├── tutorial # guide on how to SFT and GRPO\n", "│   ├── checkpoints #\n", "│   │     ├── sft # save sft checkpoints here while in tutorial\n", "│   │     ├── grpo # save grpo checkpoints here while in tutorial\n", "│   │     └── demo\n", "│   │         ├── sft # pre-trained sft (LoRA) ckpt\n", "│   │         └── grpo # same as above but for GRPO\n", "│   ├── tutorial.ipynb # guide on how to SFT and GRPO\n", "│   ├── trainer.py # sample training for <PERSON> with LoRA (SFT) and GRPO\n", "│   ├── answer_model2.py # inference script for the same. Copy it to agents/. for using this as question generator\n", "│   ├── formatted_questions_array.json # Sample question data for doing SFT and GRPO\n", "│   └── test_questions_array.json # Sample test question to evaluate the SFTed or GRPOed model\n", "├── assets\n", "│   ├── topics_example.json # example questions w.r.t each topic\n", "│   ├── topics.json # Topics on which we require to generate questions\n", "│   ├── sample_question.json # File specifying expected format of questions generated\n", "│   ├── sample_answer.json # Expected format of answers generated\n", "│   └── AMDAAIPL.png # Teaser image for the AAIPL\n", "├── utils\n", "│   └── build_prompt.py # prompt-tuning scripts\n", "├── README.ipynb\n", "├── outputs # That will consists of outputs from question_agent.py and answer_agent.py\n", "├── ckpt # That will consists of checkpoints for question_agent.py and answer_agent.py if any training is done.\n", "├── qgen.yaml # Generation specific parameters for Q-agent\n", "├── agen.yaml # Generation specific parameters for A-agent\n", "└── default_requirements.txt # Packages required\n", "```\n", "   "]}, {"cell_type": "markdown", "id": "f4fc970e", "metadata": {}, "source": ["### Env Setup\n", "<!-- 🚀 -->"]}, {"cell_type": "code", "execution_count": 1, "id": "4308a46f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting trl==0.19.0 (from -r default_requirements.txt (line 1))\n", "  Downloading trl-0.19.0-py3-none-any.whl.metadata (10 kB)\n", "Collecting wandb==0.20.1 (from -r default_requirements.txt (line 2))\n", "  Downloading wandb-0.20.1-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)\n", "Collecting ipdb==0.13.13 (from -r default_requirements.txt (line 3))\n", "  Downloading ipdb-0.13.13-py3-none-any.whl.metadata (14 kB)\n", "Collecting transformers==4.51.3 (from -r default_requirements.txt (line 4))\n", "  Downloading transformers-4.51.3-py3-none-any.whl.metadata (38 kB)\n", "Requirement already satisfied: accelerate>=1.4.0 in /usr/local/lib/python3.12/dist-packages (from trl==0.19.0->-r default_requirements.txt (line 1)) (1.7.0)\n", "Requirement already satisfied: datasets>=3.0.0 in /usr/local/lib/python3.12/dist-packages (from trl==0.19.0->-r default_requirements.txt (line 1)) (3.6.0)\n", "Requirement already satisfied: click!=8.0.0,>=7.1 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (8.1.8)\n", "Collecting gitpython!=3.1.29,>=1.0.0 (from wandb==0.20.1->-r default_requirements.txt (line 2))\n", "  Downloading GitPython-3.1.44-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (24.2)\n", "Requirement already satisfied: platformdirs in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (4.3.8)\n", "Requirement already satisfied: protobuf!=4.21.0,!=5.28.0,<7,>=3.19.0 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (5.29.4)\n", "Requirement already satisfied: psutil>=5.0.0 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (7.0.0)\n", "Requirement already satisfied: pydantic<3 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (2.11.4)\n", "Requirement already satisfied: pyyaml in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.0.0 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (2.32.3)\n", "Collecting sentry-sdk>=2.0.0 (from wandb==0.20.1->-r default_requirements.txt (line 2))\n", "  Downloading sentry_sdk-2.32.0-py2.py3-none-any.whl.metadata (10 kB)\n", "Collecting setproctitle (from wandb==0.20.1->-r default_requirements.txt (line 2))\n", "  Downloading setproctitle-1.3.6-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)\n", "Requirement already satisfied: typing-extensions<5,>=4.8 in /usr/local/lib/python3.12/dist-packages (from wandb==0.20.1->-r default_requirements.txt (line 2)) (4.13.2)\n", "Requirement already satisfied: ipython>=7.31.1 in /usr/local/lib/python3.12/dist-packages (from ipdb==0.13.13->-r default_requirements.txt (line 3)) (9.3.0)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.12/dist-packages (from ipdb==0.13.13->-r default_requirements.txt (line 3)) (5.2.1)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r default_requirements.txt (line 4)) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r default_requirements.txt (line 4)) (0.31.4)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r default_requirements.txt (line 4)) (1.26.4)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r default_requirements.txt (line 4)) (2024.11.6)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r default_requirements.txt (line 4)) (0.21.1)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r default_requirements.txt (line 4)) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.12/dist-packages (from transformers==4.51.3->-r default_requirements.txt (line 4)) (4.67.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.12/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers==4.51.3->-r default_requirements.txt (line 4)) (2025.3.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.12/dist-packages (from pydantic<3->wandb==0.20.1->-r default_requirements.txt (line 2)) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.12/dist-packages (from pydantic<3->wandb==0.20.1->-r default_requirements.txt (line 2)) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.12/dist-packages (from pydantic<3->wandb==0.20.1->-r default_requirements.txt (line 2)) (0.4.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.12/dist-packages (from requests<3,>=2.0.0->wandb==0.20.1->-r default_requirements.txt (line 2)) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.12/dist-packages (from requests<3,>=2.0.0->wandb==0.20.1->-r default_requirements.txt (line 2)) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.12/dist-packages (from requests<3,>=2.0.0->wandb==0.20.1->-r default_requirements.txt (line 2)) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.12/dist-packages (from requests<3,>=2.0.0->wandb==0.20.1->-r default_requirements.txt (line 2)) (2025.4.26)\n", "Requirement already satisfied: torch>=2.0.0 in /usr/local/lib/python3.12/dist-packages (from accelerate>=1.4.0->trl==0.19.0->-r default_requirements.txt (line 1)) (2.7.0+gitf717b2a)\n", "Requirement already satisfied: pyarrow>=15.0.0 in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (20.0.0)\n", "Requirement already satisfied: dill<0.3.9,>=0.3.0 in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (0.3.8)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (2.2.3)\n", "Requirement already satisfied: xxhash in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (3.5.0)\n", "Requirement already satisfied: multiprocess<0.70.17 in /usr/local/lib/python3.12/dist-packages (from datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (0.70.16)\n", "Requirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in /usr/local/lib/python3.12/dist-packages (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (3.11.18)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (1.6.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (6.4.4)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.12/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (1.20.0)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.12/dist-packages (from gitpython!=3.1.29,>=1.0.0->wandb==0.20.1->-r default_requirements.txt (line 2)) (4.0.12)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.12/dist-packages (from gitdb<5,>=4.0.1->gitpython!=3.1.29,>=1.0.0->wandb==0.20.1->-r default_requirements.txt (line 2)) (5.0.2)\n", "Requirement already satisfied: ipython-pygments-lexers in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (1.1.1)\n", "Requirement already satisfied: jedi>=0.16 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (0.19.2)\n", "Requirement already satisfied: matplotlib-inline in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (0.1.7)\n", "Requirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (4.9.0)\n", "Requirement already satisfied: prompt_toolkit<3.1.0,>=3.0.41 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (3.0.51)\n", "Requirement already satisfied: pygments>=2.4.0 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (2.19.1)\n", "Requirement already satisfied: stack_data in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (0.6.3)\n", "Requirement already satisfied: traitlets>=5.13.0 in /usr/local/lib/python3.12/dist-packages (from ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (5.14.3)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.12/dist-packages (from prompt_toolkit<3.1.0,>=3.0.41->ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (0.2.13)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /usr/local/lib/python3.12/dist-packages (from jedi>=0.16->ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (0.8.4)\n", "Requirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.12/dist-packages (from pexpect>4.3->ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (0.7.0)\n", "Requirement already satisfied: setuptools in /usr/local/lib/python3.12/dist-packages (from torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r default_requirements.txt (line 1)) (79.0.1)\n", "Requirement already satisfied: sympy>=1.13.3 in /usr/local/lib/python3.12/dist-packages (from torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r default_requirements.txt (line 1)) (1.14.0)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.12/dist-packages (from torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r default_requirements.txt (line 1)) (3.4.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.12/dist-packages (from torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r default_requirements.txt (line 1)) (3.1.6)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.12/dist-packages (from sympy>=1.13.3->torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r default_requirements.txt (line 1)) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.12/dist-packages (from jinja2->torch>=2.0.0->accelerate>=1.4.0->trl==0.19.0->-r default_requirements.txt (line 1)) (3.0.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.12/dist-packages (from pandas->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.12/dist-packages (from pandas->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.12/dist-packages (from pandas->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.12/dist-packages (from python-dateutil>=2.8.2->pandas->datasets>=3.0.0->trl==0.19.0->-r default_requirements.txt (line 1)) (1.17.0)\n", "Requirement already satisfied: executing>=1.2.0 in /usr/local/lib/python3.12/dist-packages (from stack_data->ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (2.2.0)\n", "Requirement already satisfied: asttokens>=2.1.0 in /usr/local/lib/python3.12/dist-packages (from stack_data->ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (3.0.0)\n", "Requirement already satisfied: pure-eval in /usr/local/lib/python3.12/dist-packages (from stack_data->ipython>=7.31.1->ipdb==0.13.13->-r default_requirements.txt (line 3)) (0.2.3)\n", "Downloading trl-0.19.0-py3-none-any.whl (375 kB)\n", "Downloading wandb-0.20.1-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (23.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m23.2/23.2 MB\u001b[0m \u001b[31m117.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading ipdb-0.13.13-py3-none-any.whl (12 kB)\n", "Downloading transformers-4.51.3-py3-none-any.whl (10.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.4/10.4 MB\u001b[0m \u001b[31m152.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading GitPython-3.1.44-py3-none-any.whl (207 kB)\n", "Downloading sentry_sdk-2.32.0-py2.py3-none-any.whl (356 kB)\n", "Downloading setproctitle-1.3.6-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (31 kB)\n", "Installing collected packages: setproctitle, sentry-sdk, gitpython, wandb, ipdb, transformers, trl\n", "\u001b[2K  Attempting uninstall: gitpython━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [sentry-sdk]\n", "\u001b[2K    Found existing installation: GitPython 0.3.6━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [sentry-sdk]\n", "\u001b[2K    Uninstalling GitPython-0.3.6:━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [sentry-sdk]\n", "\u001b[2K      Successfully uninstalled GitPython-0.3.6━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [sentry-sdk]\n", "\u001b[2K  Attempting uninstall: transformers[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3/7\u001b[0m [wandb]]\n", "\u001b[2K    Found existing installation: transformers 4.52.2━━━━━━━━━━\u001b[0m \u001b[32m3/7\u001b[0m [wandb]\n", "\u001b[2K    Uninstalling transformers-4.52.2:[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━\u001b[0m \u001b[32m5/7\u001b[0m [transformers]\n", "\u001b[2K      Successfully uninstalled transformers-4.52.2\u001b[90m━━━━━━━━━━━\u001b[0m \u001b[32m5/7\u001b[0m [transformers]\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7/7\u001b[0m [trl]\u001b[32m5/7\u001b[0m [transformers]\n", "\u001b[1A\u001b[2K\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "pypi-publisher 0.0.4 requires gitpython==0.3.6, but you have gitpython 3.1.44 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed gitpython-3.1.44 ipdb-0.13.13 sentry-sdk-2.32.0 setproctitle-1.3.6 transformers-4.51.3 trl-0.19.0 wandb-0.20.1\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["# Install the necessary packages\n", "!pip install -r default_requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "50090b9b", "metadata": {}, "outputs": [], "source": ["# import basic packages\n", "import json\n", "from typing import Dict, Any, List"]}, {"cell_type": "markdown", "id": "2187a198", "metadata": {}, "source": ["### Let the GAME begin!!!\n", "<!-- 🎮  -->\n", "#### Q-Agent\n", "<!-- 🤔 -->\n", "<u>NOTE</u>: You are encouraged to invoke your own custom code into `question_model.py` and `question_agent.py` at `agents/`, to control its operation, respectively."]}, {"cell_type": "markdown", "id": "93fdb0b0", "metadata": {}, "source": ["__Topics:__\n", "1.  `Logical Reasoning`: Truth-teller and Liar Problems\n", "2.  `Puzzles`: Seating Arrangements (Linear, Circular)\n", "3.  `Blood Relations and Family Tree`: Puzzles involving generations and family tree logic\n", "\n", "*To know what all topics are available, visit: **[topics.json](assets/topics.json)***"]}, {"cell_type": "code", "execution_count": null, "id": "4f181848", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████████████| 3/3 [00:03<00:00,  1.23s/it]\n", "STEPS: 100%|██████████████████████████████████████| 4/4 [01:22<00:00, 20.66s/it]\n", "Generated 20 questions!\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the brother of <PERSON>'s mother. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. How is <PERSON> related to I?\",\n", "  \"choices\": [\"A) A) Cousin\", \"B) B) Uncle\", \"C) C) Nephew\", \"D) D) Brother\"],\n", "  \"answer\": \"B) B) Uncle\",\n", "  \"explanation\": \"<PERSON> is the brother of <PERSON>'s mother, making <PERSON> the uncle of <PERSON>. <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the mother of <PERSON>. <PERSON> is the son of <PERSON>, who is the father of <PERSON>, who is the daughter of <PERSON>, who is the brother of <PERSON>, who is the father of <PERSON>, who is the daughter of <PERSON>, who is the son of <PERSON>, who is the mother of <PERSON>, who is the daughter of <PERSON>, who is the father of <PERSON>, who is the son of <PERSON>, who is the mother of <PERSON>. Thus, I is the granddaughter of <PERSON>, making <PERSON> the uncle of <PERSON>.\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Find the next term in the series: 2, 12, 36, 80, 150, ?\",\n", "  \"choices\": [\"A) A) 252\", \"B) B) 240\", \"C) C) 234\", \"D) D) 256\"],\n", "  \"answer\": \"A) 252\",\n", "  \"explanation\": \"The series follows the pattern of n^3 - n for n = 1, 2, 3, 4, 5, 6. For n = 6: 6^3 - 6 = 216 - 6 = 210. Wait, that doesn't fit. Let's recheck. The correct pattern is n^3 + n for n = 1, 2, 3, 4, 5, 6. For n = 6: 6^3 + 6 = 216 + 6 = 222. Still not matching. Let's re-express: The terms are 2, 12, 36, 80, 150. These can be expressed as 1^3 + 1, 2^3 + 4, 3^3 + 9, 4^3 + 16, 5^3 + 25. The added numbers are squares of 1, 2, 3, 4, 5. So for n = 6: 6^3 + 36 = 216 + 36 = 252.\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Identify the missing term in the series: 1, 1, 2, 6, 24, 120, 720, ?, 5040\",\n", "  \"choices\": [\"A) A) 5040\", \"B) B) 40320\", \"C) C) 362880\", \"D) D) 2520\"],\n", "  \"answer\": \"B) B) 40320\",\n", "  \"explanation\": \"The series represents the factorial sequence: 1!, 2!, 3!, 4!, 5!, 6!, 7!, 8!, 9!. The missing term is 8! = 40320.\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family tree, <PERSON> says, 'My father's only brother's daughter is the sister-in-law of my mother's only brother's son.' How is <PERSON> related to the son of <PERSON>'s mother's only brother?\",\n", "  \"choices\": [\"A) A) Cousin\", \"B) B) Nephew\", \"C) C) Brother\", \"D) D) Uncle\"],\n", "  \"answer\": \"A) Cousin\",\n", "  \"explanation\": \"<PERSON>'s father's only brother is <PERSON>'s uncle. His daughter is <PERSON>'s aunt. <PERSON>'s mother's only brother is <PERSON>'s uncle. His son is <PERSON>'s cousin. Thus, the son of <PERSON>'s mother's only brother is <PERSON>'s cousin.\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the only child of <PERSON> and <PERSON><PERSON> <PERSON> is the son of <PERSON> and <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON> and <PERSON><PERSON> <PERSON> is the son of <PERSON> and <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON> and <PERSON>. How is <PERSON> related to <PERSON>?\",\n", "  \"choices\": [\"A) A) Cousin\", \"B) B) <PERSON><PERSON><PERSON>\", \"C) C) Sister\", \"D) D) Aunt\"],\n", "  \"answer\": \"A) Cousin\",\n", "  \"explanation\": \"<PERSON> is the daughter of <PERSON> and <PERSON>, and <PERSON> is the brother of <PERSON>, who is the father of <PERSON>, the mother of <PERSON>, who is the son of <PERSON>, the daughter of <PERSON> and <PERSON>, who is the brother of <PERSON>, the mother of <PERSON>, who is the son of <PERSON> and <PERSON>. <PERSON> is the only child of <PERSON> and <PERSON>, making <PERSON> the niece of <PERSON><PERSON> <PERSON> is the cousin of <PERSON>, and since <PERSON> is the son of <PERSON>, <PERSON> is the cousin of <PERSON>.\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the son of the only daughter of the only son of the only brother of the only child of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother. <PERSON><PERSON> is the daughter of <PERSON><PERSON>'s paternal aunt. How is <PERSON><PERSON> related to <PERSON>?\",\n", "  \"choices\": [\"A) A) Cousin\", \"B) B) Sister\", \"C) C) <PERSON><PERSON><PERSON>\", \"D) D) Aunt\"],\n", "  \"answer\": \"A) Cousin\",\n", "  \"explanation\": \"<PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother, making <PERSON><PERSON> and <PERSON><PERSON> cousins. <PERSON> is <PERSON><PERSON>'s nephew, so <PERSON><PERSON> is <PERSON>'s cousin.\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?\",\n", "  \"choices\": [\"A) A) 372\", \"B) B) 376\", \"C) C) 378\", \"D) D) 380\"],\n", "  \"answer\": \"A) 372\",\n", "  \"explanation\": \"The pattern is each term is the sum of the previous term and the product of the previous term and its position. 2 + (2×1) = 4 (but given as 3), so the pattern is different. The actual pattern is each term is the sum of the previous term and the product of the previous term and its position minus 1. 2 + (2×1) - 1 = 3, 3 + (3×2) - 1 = 6, 6 + (6×3) - 1 = 15, 15 + (15×4) - 1 = 40, 40 + (40×5) - 1 = 123, 123 + (123×6) - 1 = 372.\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Identify the missing term in the series: 2, 4, 12, 38, 154, ?\",\n", "  \"choices\": [\"A) A) 686\", \"B) B) 712\", \"C) C) 620\", \"D) D) 658\"],\n", "  \"answer\": \"A) 686\",\n", "  \"explanation\": \"The pattern follows the recurrence relation: a(n) = 3*a(n-1) + 2*a(n-2). Calculating the next term: 3*154 + 2*38 = 462 + 76 = 686.\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"If <PERSON><PERSON>'s paternal grandmother's only son is the brother of <PERSON><PERSON>'s maternal uncle's only daughter, and <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother, how is <PERSON><PERSON> related to the son of <PERSON><PERSON>'s paternal aunt's only brother?\",\n", "  \"choices\": [\"A) A) Cousin\", \"B) B) Nephew\", \"C) C) Brother\", \"D) D) Uncle\"],\n", "  \"answer\": \"A) Cousin\",\n", "  \"explanation\": \"<PERSON><PERSON>'s paternal aunt's only brother is <PERSON><PERSON>'s paternal uncle. His son is <PERSON><PERSON>'s cousin. Thus, <PERSON><PERSON> is the cousin of the son.\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?\",\n", "  \"choices\": [\"A) A) 372\", \"B) B) 376\", \"C) C) 380\", \"D) D) 384\"],\n", "  \"answer\": \"A) 372\",\n", "  \"explanation\": \"The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so the actual pattern is more complex. The correct pattern is each term is the sum of the previous term multiplied by 3 and subtracting the term two places before. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so the actual pattern is: 2, 3, 6, 15, 40, 123, 372.\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the only son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, and <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> How is <PERSON> related to <PERSON>?\",\n", "  \"choices\": [\"A) A) Cousin\", \"B) B) <PERSON><PERSON><PERSON>\", \"C) C) Sister\", \"D) D) Aunt\"],\n", "  \"answer\": \"A) Cousin\",\n", "  \"explanation\": \"A is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, so <PERSON> is the uncle of <PERSON><PERSON> <PERSON> is the son of <PERSON>, making <PERSON> the cousin of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, making <PERSON> the cousin of <PERSON><PERSON> <PERSON> is the son of <PERSON>, making <PERSON> the cousin of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, making <PERSON> the cousin of <PERSON>. Since <PERSON> is the son of <PERSON>, <PERSON> is the cousin of <PERSON>.\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?\",\n", "  \"choices\": [\"A) A) 372\", \"B) B) 376\", \"C) C) 380\", \"D) D) 378\"],\n", "  \"answer\": \"A) 372\",\n", "  \"explanation\": \"The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so adjusted pattern is term = 3×previous - previous_previous. 2×3 - 3 = 3, 3×3 - 2 = 7 (adjusted to 6), 6×3 - 3 = 15, 15×3 - 6 = 39 (adjusted to 40), 40×3 - 15 = 105 (adjusted to 123), 123×3 - 40 = 329 (adjusted to 372).\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the father of <PERSON>, who is the sister of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the mother of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, and <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON> and the brother-in-law of <PERSON><PERSON> How is <PERSON> related to <PERSON>?\",\n", "  \"choices\": [\"A) A) Father\", \"B) B) Uncle\", \"C) C) Brother\", \"D) D) Cousin\"],\n", "  \"answer\": \"B) B) Uncle\",\n", "  \"explanation\": \"J is the brother-in-law of <PERSON>, meaning he is married to <PERSON>'s sister or wife. Since <PERSON> is <PERSON>'s father, <PERSON> is married to <PERSON>'s mother. Thus, J is <PERSON>'s uncle.\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON>'s mother's father. <PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather's only daughter. How is <PERSON> related to <PERSON><PERSON>?\",\n", "  \"choices\": [\"A) A) Brother\", \"B) B) Cousin\", \"C) C) Nephew\", \"D) D) Uncle\"],\n", "  \"answer\": \"B) Cousin\",\n", "  \"explanation\": \"<PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather's only daughter, making <PERSON>'s mother <PERSON>'s paternal aunt. <PERSON><PERSON> is <PERSON>'s mother's brother, making <PERSON> and <PERSON><PERSON> cousins.\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Identify the missing term in the series: 1, 1, 2, 6, 24, 120, ?, 5040\",\n", "  \"choices\": [\"A) A) 720\", \"B) B) 840\", \"C) C) 960\", \"D) D) 1080\"],\n", "  \"answer\": \"A) 720\",\n", "  \"explanation\": \"The series represents factorial numbers: 1! = 1, 2! = 2, 3! = 6, 4! = 24, 5! = 120, 6! = 720, 7! = 5040. The missing term is 6! = 720.\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the only son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the brother of <PERSON><PERSON> <PERSON> is the mother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. How is <PERSON> related to <PERSON>?\",\n", "  \"choices\": [\"A) A) Cousin\", \"B) B) Nephew\", \"C) C) Uncle\", \"D) D) Brother\"],\n", "  \"answer\": \"C) Uncle\",\n", "  \"explanation\": \"<PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. <PERSON>, <PERSON> and <PERSON> share a common grandparent, making <PERSON> the uncle of <PERSON>.\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Identify the missing term in the series: 2, 3, 6, 15, 42, 123, ?\",\n", "  \"choices\": [\"A) A) 366\", \"B) B) 368\", \"C) C) 364\", \"D) D) 370\"],\n", "  \"answer\": \"A) 366\",\n", "  \"explanation\": \"The series follows the pattern where each term is the sum of the previous term and the product of all previous terms. 2, 2+3=5 (but 6 is given), 3+6=9 (but 15 is given), 6+15=21 (but 42 is given), 15+42=57 (but 123 is given), 42+123=165 (but 366 is given).\"\n", "}\n", "{\n", "  \"topic\": \"Number Series\",\n", "  \"question\": \"Identify the missing term: 2, 3, 6, 15, 40, 123, ?\",\n", "  \"choices\": [\"A) A) 376\", \"B) B) 378\", \"C) C) 380\", \"D) D) 382\"],\n", "  \"answer\": \"A) 376\",\n", "  \"explanation\": \"The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before.\"\n", "}\n", "{\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"If <PERSON> is the son of the only daughter of the only brother of the mother of <PERSON><PERSON>, and <PERSON><PERSON> is the sister-in-law of the only son of the only daughter of <PERSON>'s father's mother, how is <PERSON> related to <PERSON><PERSON>?\",\n", "  \"choices\": [\"A) A) Brother\", \"B) B) Cousin\", \"C) C) Nephew\", \"D) D) Uncle\"],\n", "  \"answer\": \"D) Uncle\",\n", "  \"explanation\": \"<PERSON> is the son of the only daughter of the only brother of <PERSON><PERSON>'s mother, making <PERSON> the nephew of <PERSON><PERSON>'s mother. <PERSON><PERSON> is the sister-in-law of <PERSON>'s father's only daughter, making <PERSON><PERSON> the sister of <PERSON>'s father's daughter. Thus, <PERSON> is the uncle of <PERSON><PERSON>.\"\n", "}\n", "\n", "==================================================\n", "\n", "\n", "Time taken per batch generation: [35.13544774055481, 29.548349142074585, 8.149653673171997, 9.754874229431152]\n", "Tokens generated per batch: [2070, 5120, 1395, 1670]\n", "Total Time Taken: 82.588 seconds; Total Tokens: 10255; TGPS: 124.170 seconds\n", "\n", "\n", "\n", "++++++++++++++++++++++++++++++++++++++++++++++++++\n", "\n", "Invalid JSON format in question: {\n", "  \"topic\": \"Puzzles involving generations and family tree logic\",\n", "  \"question\": \"In a family, <PERSON> is the son of the only daughter of the only son of the only brother of the only child of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the only son of the only daughter of the\n", "Error: Unterminated string starting at: line 3 column 15 (char 82)\n", "Saved to outputs/questions.json!\n"]}], "source": ["# Run the following code to generate questions.\n", "# For demo purpose, we have used the base Qwen3-4B model for Q-Agent. Participants are expected to improve upon this\n", "!python -m agents.question_agent \\\n", "    --output_file \"outputs/questions.json\" \\\n", "    --num_questions 20 \\\n", "    --verbose"]}, {"cell_type": "markdown", "id": "9e511c33", "metadata": {}, "source": ["#### Basic format-checks for questions from Q-agent\n", "1. Here we filter questions into `questions.json` for usage of answer agent.\n", "2. Further, the filtered questions will pass through an **`Oracle`** (a part of JUDGING system, hence closed and not demonstrated here) that checks *validity* of question, choices, and answer from Q-agent. It also provides the actual correct answer to the question.\n", "3. BYOC (Bring Your Own Code): Well, again we emphasize to have your own innovations & code. Also the places with following tag/block or **similar**, expect some real improvements.\n", "    ```python\n", "    # TODO: IMPROVE THE FOLLOWING\n", "    <code>\n", "    ```\n", "4. <span style=\"color : red\">**Ensure**</span> on an average: $50\\% \\times \\text{num\\_questions} > N$ questions are filtered out.\n", "5. The following filter is added into the `question_agent.py`. *<span style=\"color : red\">Note that</span> we generate two version of questions, one is the usual, unfiltered one `questions.json` and the other is `filtered_questions.json` after passing through the below filter. <span style=\"color : green\">We'll use this `filtered_questions.json` for conducting matches i.e., this file will be sent to opponent's answer agent. But do keep both in `outputs/` folder.</span>*\n"]}, {"cell_type": "code", "execution_count": null, "id": "a3770ec5", "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer\n", "tokenizer = AutoTokenizer.from_pretrained(\"/jupyter-tutorial/hf_models/Qwen3-4B\", padding_side='left')\n", "\n", "def count_tokens_q(text: str) -> int:\n", "    \"\"\"Count the number of tokens using Qwen3-4B tokenizer\"\"\"\n", "    return len(tokenizer.encode(text, add_special_tokens=False))\n", "\n", "def filter_questions(questions: List[str|Dict[str, str|Any]]) -> List[Dict[str, str|Any]]:\n", "    def basic_checks(q2: Dict[str, str])->bool:\n", "        # check required keys\n", "        required_keys = ['topic', 'question', 'choices', 'answer']\n", "        if all((key in q2) for key in required_keys):\n", "            # check choices format\n", "            checks = all(isinstance(choice, str) and len(choice) > 2 and choice[0].upper() in 'ABCD' for choice in q2['choices'])\n", "            if isinstance(q2['choices'], list) and len(q2['choices']) == 4 and checks:\n", "                # check answer format\n", "                # Check token length\n", "                check_len = sum(count_tokens_q(q2[k]) for k in ['question', 'answer'])\n", "                check_len += sum(count_tokens_q(choice) for choice in q2['choices']) - 15\n", "                if check_len < 130:\n", "                    if check_len + count_tokens_q(q2.get('explanation', 'None')) <= 1024:\n", "                        # Extra Checks: (PLUS checks) len(q2['answer']) == 1 and q2['answer'].upper() in 'ABCD':\n", "                        if isinstance(q2['answer'], str):\n", "                            return True\n", "        return False\n", "    correct_format_question = []\n", "    for i, q in enumerate(questions):\n", "        if isinstance(q, dict):\n", "            if basic_checks(q):\n", "                correct_format_question.append(q)\n", "        elif isinstance(q, str):\n", "            try:\n", "                q1 = json.loads(q)\n", "                if basic_checks(q1):\n", "                    correct_format_question.append(q1)\n", "            except json.JSONDecodeError:\n", "                # If JSON decoding fails, skip this answer\n", "                print(f\"Skipping invalid JSON at index {i}: {q}\")\n", "                continue\n", "        else:\n", "            continue\n", "    if len(correct_format_question) >= 0.5 * len(questions):\n", "        return correct_format_question\n", "    return list()"]}, {"cell_type": "code", "execution_count": null, "id": "8a66e521", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'topic': 'Puzzles involving generations and family tree logic', 'question': \"In a family, <PERSON> is the brother of <PERSON>'s mother. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. <PERSON> is the son of <PERSON>. <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON>. <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the son of <PERSON>. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON>. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. How is <PERSON> related to I?\", 'choices': ['A) A) Cousin', 'B) B) Uncle', 'C) C) Nephew', 'D) D) Brother'], 'answer': 'B) B) Uncle', 'explanation': \"<PERSON> is the brother of <PERSON>'s mother, making <PERSON> the uncle of <PERSON>. <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the mother of <PERSON>. <PERSON> is the son of <PERSON>, who is the father of <PERSON>, who is the daughter of <PERSON>, who is the brother of <PERSON>, who is the father of <PERSON>, who is the daughter of <PERSON>, who is the son of <PERSON>, who is the mother of <PERSON>, who is the daughter of <PERSON>, who is the father of <PERSON>, who is the son of <PERSON>, who is the mother of I. Thus, I is the granddaughter of <PERSON>, making <PERSON> the uncle of I.\"}\n", "{'topic': 'Number Series', 'question': 'Find the next term in the series: 2, 12, 36, 80, 150, ?', 'choices': ['A) A) 252', 'B) B) 240', 'C) C) 234', 'D) D) 256'], 'answer': 'A) 252', 'explanation': \"The series follows the pattern of n^3 - n for n = 1, 2, 3, 4, 5, 6. For n = 6: 6^3 - 6 = 216 - 6 = 210. Wait, that doesn't fit. Let's recheck. The correct pattern is n^3 + n for n = 1, 2, 3, 4, 5, 6. For n = 6: 6^3 + 6 = 216 + 6 = 222. Still not matching. Let's re-express: The terms are 2, 12, 36, 80, 150. These can be expressed as 1^3 + 1, 2^3 + 4, 3^3 + 9, 4^3 + 16, 5^3 + 25. The added numbers are squares of 1, 2, 3, 4, 5. So for n = 6: 6^3 + 36 = 216 + 36 = 252.\"}\n", "{'topic': 'Number Series', 'question': 'Identify the missing term in the series: 1, 1, 2, 6, 24, 120, 720, ?, 5040', 'choices': ['A) A) 5040', 'B) B) 40320', 'C) C) 362880', 'D) D) 2520'], 'answer': 'B) B) 40320', 'explanation': 'The series represents the factorial sequence: 1!, 2!, 3!, 4!, 5!, 6!, 7!, 8!, 9!. The missing term is 8! = 40320.'}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': \"In a family tree, <PERSON> says, 'My father's only brother's daughter is the sister-in-law of my mother's only brother's son.' How is <PERSON> related to the son of <PERSON>'s mother's only brother?\", 'choices': ['A) A) Cousin', 'B) B) Nephew', 'C) C) Brother', 'D) D) Uncle'], 'answer': 'A) Cousin', 'explanation': \"<PERSON>'s father's only brother is <PERSON>'s uncle. His daughter is <PERSON>'s aunt. <PERSON>'s mother's only brother is <PERSON>'s uncle. His son is <PERSON>'s cousin. Thus, the son of <PERSON>'s mother's only brother is <PERSON>'s cousin.\"}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': 'In a family, <PERSON> is the only child of <PERSON> and <PERSON>. <PERSON> is the son of <PERSON> and <PERSON>. <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON> and <PERSON>. <PERSON> is the son of <PERSON> and <PERSON>. <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the father of <PERSON>. <PERSON> is the brother of <PERSON>. <PERSON> is the daughter of <PERSON> and <PERSON>. How is <PERSON> related to <PERSON>?', 'choices': ['A) A) Cousin', 'B) B) Niece', 'C) C) Sister', 'D) D) Aunt'], 'answer': 'A) Cousin', 'explanation': '<PERSON> is the daughter of <PERSON> and <PERSON>, and <PERSON> is the brother of <PERSON>, who is the father of <PERSON>, the mother of <PERSON>, who is the son of <PERSON>, the daughter of <PERSON> and <PERSON>, who is the brother of <PERSON>, the mother of <PERSON>, who is the son of <PERSON> and <PERSON>. <PERSON> is the only child of <PERSON> and <PERSON>, making <PERSON> the niece of <PERSON>. <PERSON> is the cousin of <PERSON>, and since <PERSON> is the son of <PERSON>, <PERSON> is the cousin of <PERSON>.'}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': \"In a family, <PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother. <PERSON><PERSON> is the daughter of <PERSON><PERSON>'s paternal aunt. How is <PERSON><PERSON> related to <PERSON>?\", 'choices': ['A) A) Cousin', 'B) B) Sister', 'C) C) <PERSON>ece', 'D) D) Aunt'], 'answer': 'A) Cousin', 'explanation': \"<PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother, making <PERSON><PERSON> and <PERSON><PERSON> cousins. <PERSON> is <PERSON><PERSON>'s nephew, so <PERSON><PERSON> is <PERSON>'s cousin.\"}\n", "{'topic': 'Number Series', 'question': 'Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?', 'choices': ['A) A) 372', 'B) B) 376', 'C) C) 378', 'D) D) 380'], 'answer': 'A) 372', 'explanation': 'The pattern is each term is the sum of the previous term and the product of the previous term and its position. 2 + (2×1) = 4 (but given as 3), so the pattern is different. The actual pattern is each term is the sum of the previous term and the product of the previous term and its position minus 1. 2 + (2×1) - 1 = 3, 3 + (3×2) - 1 = 6, 6 + (6×3) - 1 = 15, 15 + (15×4) - 1 = 40, 40 + (40×5) - 1 = 123, 123 + (123×6) - 1 = 372.'}\n", "{'topic': 'Number Series', 'question': 'Identify the missing term in the series: 2, 4, 12, 38, 154, ?', 'choices': ['A) A) 686', 'B) B) 712', 'C) C) 620', 'D) D) 658'], 'answer': 'A) 686', 'explanation': 'The pattern follows the recurrence relation: a(n) = 3*a(n-1) + 2*a(n-2). Calculating the next term: 3*154 + 2*38 = 462 + 76 = 686.'}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': \"If <PERSON><PERSON>'s paternal grandmother's only son is the brother of <PERSON><PERSON>'s maternal uncle's only daughter, and <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother, how is <PERSON><PERSON> related to the son of <PERSON><PERSON>'s paternal aunt's only brother?\", 'choices': ['A) A) Cousin', 'B) B) Nephew', 'C) C) Brother', 'D) D) Uncle'], 'answer': 'A) Cousin', 'explanation': \"<PERSON><PERSON>'s paternal aunt's only brother is <PERSON><PERSON>'s paternal uncle. His son is <PERSON><PERSON>'s cousin. Thus, <PERSON><PERSON> is the cousin of the son.\"}\n", "{'topic': 'Number Series', 'question': 'Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?', 'choices': ['A) A) 372', 'B) B) 376', 'C) C) 380', 'D) D) 384'], 'answer': 'A) 372', 'explanation': 'The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so the actual pattern is more complex. The correct pattern is each term is the sum of the previous term multiplied by 3 and subtracting the term two places before. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so the actual pattern is: 2, 3, 6, 15, 40, 123, 372.'}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': 'In a family, <PERSON> is the only son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, and <PERSON> is the son of <PERSON>. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>. How is <PERSON> related to A?', 'choices': ['A) A) Cousin', 'B) B) <PERSON>ece', 'C) C) Sister', 'D) D) Aunt'], 'answer': 'A) Cousin', 'explanation': '<PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, so <PERSON> is the uncle of <PERSON>. <PERSON> is the son of <PERSON>, making <PERSON> the cousin of <PERSON>. <PERSON> is the daughter of <PERSON>, making <PERSON> the cousin of <PERSON>. <PERSON> is the son of <PERSON>, making <PERSON> the cousin of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, making <PERSON> the cousin of <PERSON>. Since <PERSON> is the son of <PERSON>, <PERSON> is the cousin of <PERSON>.'}\n", "{'topic': 'Number Series', 'question': 'Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?', 'choices': ['A) A) 372', 'B) B) 376', 'C) C) 380', 'D) D) 378'], 'answer': 'A) 372', 'explanation': 'The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so adjusted pattern is term = 3×previous - previous_previous. 2×3 - 3 = 3, 3×3 - 2 = 7 (adjusted to 6), 6×3 - 3 = 15, 15×3 - 6 = 39 (adjusted to 40), 40×3 - 15 = 105 (adjusted to 123), 123×3 - 40 = 329 (adjusted to 372).'}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': 'In a family, <PERSON> is the father of <PERSON>, who is the sister of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the mother of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, and <PERSON> is the daughter of <PERSON>. <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON> and the brother-in-law of <PERSON>. How is <PERSON> related to B?', 'choices': ['A) A) Father', 'B) B) Uncle', 'C) C) Brother', 'D) D) Cousin'], 'answer': 'B) B) Uncle', 'explanation': \"J is the brother-in-law of <PERSON>, meaning he is married to <PERSON>'s sister or wife. Since <PERSON> is <PERSON>'s father, <PERSON> is married to <PERSON>'s mother. Thus, <PERSON> is <PERSON>'s uncle.\"}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': \"In a family, <PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON>'s mother's father. <PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather's only daughter. How is <PERSON> related to <PERSON><PERSON>?\", 'choices': ['A) A) Brother', 'B) B) Cousin', 'C) C) Nephew', 'D) D) Uncle'], 'answer': 'B) Cousin', 'explanation': \"<PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather's only daughter, making <PERSON>'s mother <PERSON>'s paternal aunt. <PERSON><PERSON> is <PERSON>'s mother's brother, making <PERSON> and <PERSON><PERSON> cousins.\"}\n", "{'topic': 'Number Series', 'question': 'Identify the missing term in the series: 1, 1, 2, 6, 24, 120, ?, 5040', 'choices': ['A) A) 720', 'B) B) 840', 'C) C) 960', 'D) D) 1080'], 'answer': 'A) 720', 'explanation': 'The series represents factorial numbers: 1! = 1, 2! = 2, 3! = 6, 4! = 24, 5! = 120, 6! = 720, 7! = 5040. The missing term is 6! = 720.'}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': 'In a family, <PERSON> is the only son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the brother of <PERSON><PERSON> <PERSON> is the mother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the mother of <PERSON>. <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. How is <PERSON> related to V?', 'choices': ['A) A) Cousin', 'B) B) Nephew', 'C) C) Uncle', 'D) D) Brother'], 'answer': 'C) Uncle', 'explanation': '<PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. <PERSON>, <PERSON> and <PERSON> share a common grandparent, making <PERSON> the uncle of <PERSON>.'}\n", "{'topic': 'Number Series', 'question': 'Identify the missing term in the series: 2, 3, 6, 15, 42, 123, ?', 'choices': ['A) A) 366', 'B) B) 368', 'C) C) 364', 'D) D) 370'], 'answer': 'A) 366', 'explanation': 'The series follows the pattern where each term is the sum of the previous term and the product of all previous terms. 2, 2+3=5 (but 6 is given), 3+6=9 (but 15 is given), 6+15=21 (but 42 is given), 15+42=57 (but 123 is given), 42+123=165 (but 366 is given).'}\n", "{'topic': 'Number Series', 'question': 'Identify the missing term: 2, 3, 6, 15, 40, 123, ?', 'choices': ['A) A) 376', 'B) B) 378', 'C) C) 380', 'D) D) 382'], 'answer': 'A) 376', 'explanation': 'The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before.'}\n", "{'topic': 'Puzzles involving generations and family tree logic', 'question': \"If <PERSON> is the son of the only daughter of the only brother of the mother of <PERSON><PERSON>, and <PERSON><PERSON> is the sister-in-law of the only son of the only daughter of <PERSON>'s father's mother, how is <PERSON> related to <PERSON><PERSON>?\", 'choices': ['A) A) Brother', 'B) B) Cousin', 'C) C) Nephew', 'D) D) Uncle'], 'answer': 'D) Uncle', 'explanation': \"<PERSON> is the son of the only daughter of the only brother of <PERSON><PERSON>'s mother, making <PERSON> the nephew of <PERSON><PERSON>'s mother. <PERSON><PERSON> is the sister-in-law of <PERSON>'s father's only daughter, making <PERSON><PERSON> the sister of <PERSON>'s father's daughter. Thus, <PERSON> is the uncle of <PERSON><PERSON>.\"}\n"]}], "source": ["with open(\"outputs/questions.json\", \"r\") as f:\n", "    questions = json.load(f)\n", "\n", "filtered_questions = filter_questions(questions)\n", "\n", "# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "# Further filtering will happen with our Oracle (not shown here) which also have its own answer for the question.\n", "# If Q-agent answer to its own question is wrong, then that question will not be considered.\n", "# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "\n", "with open(\"outputs/filtered_questions.json\", \"w\") as f:\n", "    json.dump(filtered_questions, f, indent=4)"]}, {"cell_type": "code", "execution_count": 21, "id": "ba3fe904", "metadata": {}, "outputs": [{"data": {"text/plain": ["19"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(filtered_questions)"]}, {"cell_type": "markdown", "id": "8f96209d", "metadata": {}, "source": ["#### A-agent\n", "<!-- 🤖  -->\n", "<u>NOTE</u>: Here in the `answer_agent.py` you can integrate your custom model -- ***E.g., SFTed or GRPOed model [answer_model2.py](./tutorial/answer_model2.py)**. But first do SFT / GRPO -> load the checkpoint with correct path in [answer_model2.py](./tutorial/answer_model2.py) and then integrate it into `answer_agent.py`.*"]}, {"cell_type": "code", "execution_count": null, "id": "d51af0a4", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████████████| 2/2 [00:03<00:00,  1.50s/it]\n", "STEPS: : 5batch [01:07, 13.55s/batch]                                           \n", "\n", "=== Question 1 ===\n", "Question: In a family, <PERSON> is the brother of <PERSON>'s mother. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. How is <PERSON> related to I?\n", "Expected: B) B) Uncle\n", "Model Answer:\n", "{\n", "  \"answer\": \"C\",\n", "  \"reasoning\": \"To find the relationship between <PERSON> and <PERSON>, we can work backwards from I. I is the daughter of <PERSON>, and <PERSON> is the father of <PERSON>. Following this pattern, <PERSON> is the son of <PERSON>, and <PERSON> is the daughter of <PERSON>. Continuing, <PERSON> is the mother of <PERSON>, and <PERSON> is the son of <PERSON>. Since <PERSON> is the father of <PERSON>, and <PERSON> is the daughter of <PERSON>, <PERSON> is the mother of <PERSON>. This makes <PERSON> the grandmother of <PERSON>. Therefore, <PERSON> is the grandmother of <PERSON>, which is the same as saying <PERSON> is I's great-aunt, making <PERSON> the aunt of <PERSON>, but since the options are limited to immediate familial relationships, the correct answer is 'C) Nephew' is incorrect. The correct answer is 'C) Uncle' is also incorrect, but 'C) Nephew' is the only option that is a possible correct answer in this scenario. The correct answer is 'C) Nephew' is incorrect, but 'C) Uncle' is incorrect. Since 'A) Cousin' is also incorrect, 'C) Nephew' is incorrect. The only option left is 'C) Uncle', which is incorrect. Therefore, the only remaining option is 'C) Nephew' is incorrect. The only remaining option is 'C) Uncle', which is incorrect. The only remaining option is 'C) Nephew' is incorrect. The only remaining option is 'C) Uncle', which is incorrect. Since 'A) Cousin' is incorrect, 'C) Nephew' is incorrect. The only option left is 'C) <PERSON>', which is incorrect. Since 'A) Cousin' is incorrect, 'C) Nephew' is incorrect. The correct answer is 'C) Uncle' is incorrect. The correct answer is 'C) Nephew' is incorrect. The correct answer is 'C) Uncle' is incorrect. The correct answer is 'C) Nephew' is incorrect. The correct answer is 'C) Uncle' is incorrect. The correct answer is 'C) Nephew' is incorrect. The correct answer is 'C) Uncle' is incorrect. The correct answer is 'C) Nephew' is incorrect. The correct answer is 'C) Uncle' is incorrect. The correct answer is 'C) Nephew' is incorrect. The correct answer is 'C) Uncle\n", "\n", "=== Question 2 ===\n", "Question: Find the next term in the series: 2, 12, 36, 80, 150, ?\n", "Expected: A) 252\n", "Model Answer:\n", "{\n", "    \"answer\": \"A\",\n", "    \"reasoning\": \"The given series appears to be formed by adding 10, then 20, then 40, then 70, which are doubling the increments. Following this pattern, the next increment should be 140. Adding 140 to 150 results in 290, but this is not an option. However, if we re-evaluate the pattern, we can notice that each term is increasing by consecutive increments of 10, 20, 40, 70. The next increment should be 140, but since this is not available, we can try adding 140 to the last term (150) and subtracting the next available increment (70) to get 80, and then add 70 to 80 to get 150, and then add 140 to 150 to get 290. However, the correct pattern is obtained by adding 10, 20, 40, 70, and then 140, but the next available increment after 150 is 210. So, the correct next term is 150 + 210 = 360, but this is not an option. However, if we re-evaluate the series again, we can notice that the series can be represented as 2 + 10, 2 + 10 + 20, 2 + 10 + 20 + 40, 2 + 10 + 20 + 40 + 70, and so on. The next term would be 2 + 10 + 20 + 40 + 70 + 140 = 262. But this is not an option. Looking at the options again, the correct answer is A) 252, which is 2 + 10 + 20 + 40 + 70 + 70. This is the closest available option, although not the exact answer. However, the correct next term in the series is not among the options, but option A) 252 is the closest available answer.\"\n", "}\n", "\n", "=== Question 3 ===\n", "Question: Identify the missing term in the series: 1, 1, 2, 6, 24, 120, 720, ?, 5040\n", "Expected: B) B) 40320\n", "Model Answer:\n", "{\n", "    \"answer\": \"B) 40320\",\n", "    \"reasoning\": \"The given series is formed by multiplying the previous term by the next integer: 1*2=2, 2*3=6, 6*4=24, 24*5=120, 120*6=720, 720*7=5040, 5040*8=40320. This pattern indicates that each term is the result of multiplying the previous term by consecutive integers, making the next term 40320.\"\n", "}\n", "\n", "=== Question 4 ===\n", "Question: In a family tree, <PERSON> says, 'My father's only brother's daughter is the sister-in-law of my mother's only brother's son.' How is <PERSON> related to the son of <PERSON>'s mother's only brother?\n", "Expected: A) <PERSON><PERSON><PERSON>\n", "Model Answer:\n", "{\n", "  \"answer\": \"B\",\n", "  \"reasoning\": \"Let's break down the information given. <PERSON>'s father has a brother, and that brother's daughter is the sister-in-law of <PERSON>'s mother's only brother's son. This implies that <PERSON>'s mother is the sister of the brother of the son. Since the son is the brother of <PERSON>'s mother's brother, the son is <PERSON>'s first cousin. Therefore, <PERSON> is the first cousin of the son of <PERSON>'s mother's only brother.\"\n", "}\n", "\n", "=== Question 5 ===\n", "Question: In a family, <PERSON> is the only child of <PERSON> and <PERSON><PERSON> <PERSON> is the son of <PERSON> and <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON> and <PERSON><PERSON> <PERSON> is the son of <PERSON> and <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON> and <PERSON>. How is <PERSON> related to <PERSON>?\n", "Expected: A) <PERSON><PERSON><PERSON>\n", "Model Answer:\n", "{\n", "    \"answer\": \"A\",\n", "    \"reasoning\": \"<PERSON> is the daughter of <PERSON> and <PERSON>. Since <PERSON> is the brother of <PERSON> and <PERSON> is the father of <PERSON>, making <PERSON> the child of <PERSON>'s sibling. As <PERSON> is the brother of <PERSON> and <PERSON> is the parent of <PERSON>, making <PERSON> the grandchild of <PERSON>'s sibling. Given that <PERSON> is the child of <PERSON> and <PERSON>, and <PERSON> is the sibling of <PERSON>, making <PERSON> the grandchild of <PERSON>'s sibling. Therefore, <PERSON> is the first cousin of <PERSON>.\"\n", "}\n", "\n", "=== Question 6 ===\n", "Question: In a family, <PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother. <PERSON><PERSON> is the daughter of <PERSON><PERSON>'s paternal aunt. How is <PERSON><PERSON> related to <PERSON>?\n", "Expected: A) <PERSON><PERSON><PERSON>\n", "Model Answer:\n", "{\n", "  \"answer\": \"C\",\n", "  \"reasoning\": \"Let's break down the relationships: <PERSON> is the son of <PERSON><PERSON>'s sister, making <PERSON> the nephew of <PERSON><PERSON>. <PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother, making <PERSON><PERSON> the uncle of <PERSON><PERSON>. Since <PERSON><PERSON> is the daughter of <PERSON><PERSON>'s paternal aunt, <PERSON><PERSON> is the niece of <PERSON><PERSON>. Therefore, <PERSON> is the nephew of <PERSON><PERSON>'s niece <PERSON><PERSON>, making <PERSON><PERSON>'s niece.\"\n", "}\n", "\n", "=== Question 7 ===\n", "Question: Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?\n", "Expected: A) 372\n", "Model Answer:\n", "{\n", "    \"answer\": \"A\",\n", "    \"reasoning\": \"The given series appears to be formed by adding 1, 3, 9, 15, 27,... to the previous term. This pattern suggests that each term is increasing by consecutive squares (1^2, 3^2, 3^2 + 6^2, 3^2 + 6^2 + 9^2, 3^2 + 6^2 + 9^2 + 12^2,...). Following this pattern, the next term would be 3^2 + 6^2 + 9^2 + 12^2 + 15^2 = 3^2 + 6^2 + 9^2 + 12^2 + 15^2 = 372\"\n", "}\n", "\n", "=== Question 8 ===\n", "Question: Identify the missing term in the series: 2, 4, 12, 38, 154, ?\n", "Expected: A) 686\n", "Model Answer:\n", "{\n", "    \"answer\": \"A\",\n", "    \"reasoning\": \"The given series appears to be formed by adding 2, then 8, then 26, then 116. The differences between the terms are increasing by a factor of 4, 3.25, and 4.17, respectively. The pattern suggests that the differences are not consistent, but the differences of the differences are increasing by a factor of 1.0625 (4/3.75). This pattern is consistent with a sequence of numbers that are one less than a power of 2. Therefore, the next difference should be 4.17 * 4.0625 = 17, making the next term 154 + 17 = 171, which is not an option. However, 171 - 7 = 164 is not an option, but 171 + 7 = 178 is not an option, but 171 + 17 = 188 is not an option, but 171 + 4 * 17 = 195 is not an option, but 171 + 4 * 4 * 4.0625 = 171 + 68 = 239 is not an option, but 171 + 4 * 4 * 3.25 = 171 + 54 = 225 is not an option, but 171 + 4 * 3.25 * 3.75 = 171 + 48 = 219 is not an option, but 171 + 4 * 3.25 * 1.0625 = 171 + 14 = 185 is not an option, but 171 + 4 * 3.25 * 1.0625 * 1.0625 = 171 + 6.25 = 177 is not an option, but 171 + 4 * 3.25 * 1.0625 * (1.0625)^2 = 171 + 7 = 178 is not an option, but 171 + 4 * 3.25 * 1.0625 * (1.0625)^2 * 1.0625 = 171 + 8 = 179 is not an option, but 171 + 4 * 3.25 * 1.0625 * (1.0625)^2 * 1.0625 = 171 + 9 = 180 is not an option, but 171 + 4 *\n", "\n", "=== Question 9 ===\n", "Question: If <PERSON><PERSON>'s paternal grandmother's only son is the brother of <PERSON><PERSON>'s maternal uncle's only daughter, and <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother, how is <PERSON><PERSON> related to the son of <PERSON><PERSON>'s paternal aunt's only brother?\n", "Expected: A) <PERSON><PERSON><PERSON>\n", "Model Answer:\n", "{\n", "  \"answer\": \"C\",\n", "  \"reasoning\": \"Let's break down the information: <PERSON><PERSON>'s paternal grandmother's only son is the brother of <PERSON><PERSON>'s maternal uncle's only daughter. This means <PERSON><PERSON>'s maternal uncle is the brother of <PERSON><PERSON>'s paternal grandmother's only son. <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother. Since <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother, <PERSON><PERSON>'s paternal aunt's only brother is <PERSON><PERSON>'s maternal uncle's son. Therefore, <PERSON><PERSON> is the niece of <PERSON><PERSON>'s paternal aunt's only brother, making <PERSON><PERSON> the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, which makes <PERSON><PERSON> the niece of <PERSON><PERSON>'s paternal aunt's only brother's son, or the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, or simply the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, or the niece of <PERSON><PERSON>'s paternal aunt's only brother's son, which is the same as saying <PERSON><PERSON> is the niece of <PERSON><PERSON>'s paternal aunt's only brother's son. <PERSON><PERSON> is not the brother, cousin, or uncle of the son of <PERSON><PERSON>'s paternal aunt's only brother. Therefore, <PERSON><PERSON> is the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother.\"\n", "}\n", "\n", "=== Question 10 ===\n", "Question: Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?\n", "Expected: A) 372\n", "Model Answer:\n", "{\n", "    \"answer\": \"A\",\n", "    \"reasoning\": \"The given series appears to be formed by adding 1, 3, 9, 15, 27,... to the previous term. These increments are squares of consecutive integers (1^2, 2^2, 3^2, 4^2, 5^2). Following this pattern, the next increment should be 6^2 = 36. Adding 36 to the last term (123) results in 159, which does not match any of the provided options. However, if we re-evaluate the increments, we notice that each term is also 1 less than the next perfect square (2+1=3, 3+1=4, 6+1=7, 15+1=16, 40+1=41, 123+1=124). Following this corrected pattern, the next term should be 124+1=125, but this is not an option. Upon re-examining the original series, it seems the increments are actually consecutive integers (1, 3, 6, 15, 40, 123). If this is the case, the next difference should be 78, making the next term 123 + 78 = 201, which is not an option. The pattern of consecutive integers seems incorrect, so let's re-examine the series again. The correct pattern appears to be formed by adding consecutive integers (1, 2, 5, 10, 25,...). Following this pattern, the next difference should be 25*2=50, making the next term 123+50=173, which is not an option. However, the correct pattern seems to be 2, 3, 2*3, 2*3*2+1, 2*3*2*3+1, 2*3*2*3*2+1,.... Following this corrected pattern, the next term should be 2*3*2*3*2*3+1 = 2*3^5+1 = 2*243+1 = 487, which is not an option. Re-examining the series again, the correct pattern appears to be formed by adding 1, 2, 3, 7, 11, 23,... to the previous term. The differences between the terms are consecutive prime numbers. Following\n", "\n", "=== Question 11 ===\n", "Question: In a family, <PERSON> is the only son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, and <PERSON> is the son of <PERSON>. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> How is <PERSON> related to <PERSON>?\n", "Expected: A) <PERSON><PERSON><PERSON>\n", "Model Answer:\n", "{\n", "    \"answer\": \"C\",\n", "    \"reasoning\": \"Start by tracing the family relationships from H to A. <PERSON> is the daughter of <PERSON>, G is the daughter of <PERSON>, F is the daughter of <PERSON>, E is the son of <PERSON>, <PERSON> is the brother of <PERSON>, and <PERSON> is the parent of <PERSON>. This forms a chain of relationships: H -> G -> F -> E -> D -> C -> A. Since <PERSON> is the parent of A, H is the niece of <PERSON>.\"\n", "}\n", "\n", "=== Question 12 ===\n", "Question: Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?\n", "Expected: A) 372\n", "Model Answer:\n", "{\n", "  \"answer\": \"A\",\n", "  \"reasoning\": \"The given series is formed by adding 1, 3, 9, 15, 27,... to the previous term. These numbers are squares of 1, 2, 3, 4, 5,.... Therefore, the next number in the series would be the square of 6, which is 36. Adding 36 to 123 results in 159. However, none of the options match this result. Looking at the pattern again, we can see that each term is the sum of the previous term and the square of the next integer. Following this pattern, the next term should be 123 + 36 + 45 (which is the square of 6 and 7, respectively) = 204. However, this is not an option. Upon closer inspection, we notice that the series is formed by adding consecutive triangular numbers (1+2=3, 3+4=7, 7+9=16, 16+15=31, 31+39=70, 70+123=193, 193+240=433). Therefore, the next triangular number is 433 + 300 = 733, but this is not an option. However, if we look at the difference between the options, we can see that 380 - 123 = 257, and 257 - 123 = 134, and 134 - 123 = 11, and 11 - 10 = 1. Following this pattern, 1 + 2 + 3 +... + 11 = 66, but this is not the correct answer. However, 1 + 2 + 3 +... + 10 = 55, and 55 + 26 = 81. Since 81 is not an option, let's try 1 + 2 + 3 +... + 9 = 45, and 45 + 39 = 84. However, this is not an option. But 1 + 2 + 3 +... + 8 = 36, and 36 + 48 = 84, which is also not an option. However, 1 + 2 + 3 +... + 7 = 28, and 28 + 51 = 79, which is not an option. 1 + 2 + 3 +... + 6 = 21, and\n", "\n", "=== Question 13 ===\n", "Question: In a family, <PERSON> is the father of <PERSON>, who is the sister of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the mother of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, and <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON> and the brother-in-law of <PERSON><PERSON> How is <PERSON> related to <PERSON>?\n", "Expected: B) B) Uncle\n", "Model Answer:\n", "{\n", "  \"answer\": \"C\",\n", "  \"reasoning\": \"Given the relationships, we can deduce that <PERSON> is the sister of <PERSON>. Since <PERSON> is the brother-in-law of <PERSON>, and <PERSON> is the father of <PERSON>, <PERSON> must be the brother of <PERSON>'s mother. As <PERSON> is the mother of <PERSON>, and <PERSON> is the brother of <PERSON>, <PERSON> is the brother of <PERSON>'s mother. Therefore, <PERSON> is the uncle of <PERSON>. Since <PERSON> is the brother of <PERSON>'s mother, <PERSON> is the uncle of <PERSON>.\"\n", "}\n", "\n", "=== Question 14 ===\n", "Question: In a family, <PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON>'s mother's father. <PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather's only daughter. How is <PERSON> related to <PERSON><PERSON>?\n", "Expected: B) Cousin\n", "Model Answer:\n", "{\n", "  \"answer\": \"A\",\n", "  \"reasoning\": \"<PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON>'s mother's father. This implies that <PERSON><PERSON>'s father is the brother of <PERSON>'s paternal grandfather. Since <PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather, <PERSON><PERSON> is <PERSON>'s maternal aunt. Therefore, <PERSON> is her nephew. The correct answer is A) Brother is incorrect because <PERSON> is not <PERSON><PERSON>'s brother. The correct relationship is that of a nephew, making option A the correct answer.\"\n", "}\n", "\n", "=== Question 15 ===\n", "Question: Identify the missing term in the series: 1, 1, 2, 6, 24, 120, ?, 5040\n", "Expected: A) 720\n", "Model Answer:\n", "{\n", "  \"answer\": \"A\",\n", "  \"reasoning\": \"The series appears to be formed by multiplying the previous term by a growing factorial: 1*2=2, 2*3=6, 6*4=24, 24*5=120, 120*6=720. Following this pattern, the next term should be 720*7=5040, but the question asks for the missing term. Therefore, the correct answer is A) 720.\"\n", "}\n", "\n", "=== Question 16 ===\n", "Question: In a family, <PERSON> is the only son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the brother of <PERSON><PERSON> <PERSON> is the mother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. How is <PERSON> related to <PERSON>?\n", "Expected: C) Uncle\n", "Model Answer:\n", "{\n", "  \"answer\": \"A\",\n", "  \"reasoning\": \"We can analyze the relationships step by step. First, we find that Z is the mother of <PERSON> and <PERSON> is the father of Y. Then, we find that <PERSON> is the son of W and Y is the daughter of <PERSON>. Therefore, Y is the daughter of X. Since X is the father of Y, <PERSON> is the grandfather of Y. We continue this pattern and find that <PERSON> is the son of Z and Z is the mother of <PERSON>. Therefore, A is the son of Z, making <PERSON> the nephew of Y, and since Y is the daughter of W, A is the nephew of <PERSON>. Hence, <PERSON> is a cousin of <PERSON>, who is the daughter of <PERSON>, making <PERSON> a cousin of <PERSON>.\"\n", "}\n", "\n", "=== Question 17 ===\n", "Question: Identify the missing term in the series: 2, 3, 6, 15, 42, 123, ?\n", "Expected: A) 366\n", "Model Answer:\n", "{\n", "  \"answer\": \"A\",\n", "  \"reasoning\": \"The given series appears to be formed by adding 1, 3, 9, 21, 99,... (doubling the previous increment) to the previous term. Following this pattern, the next increment should be 189 (99 * 2). Adding 189 to 123 results in 312. However, none of the answer options match this calculation. Upon re-examining the series, it seems to be formed by adding consecutive squares (1^2 = 1, 2^2 = 4, 3^2 = 9, 4^2 = 16, 5^2 = 25, 6^2 = 36, 7^2 = 49, 8^2 = 64, 9^2 = 81, 10^2 = 100, 11^2 = 121, 12^2 = 144, 13^2 = 169, 14^2 = 196, 15^2 = 225, 16^2 = 256, 17^2 = 289, 18^2 = 324, 19^2 = 361). Therefore, the next term should be 19^2 = 361, which matches option C) 364 is incorrect, but 19^2 + 1 = 361 + 1 = 362 is close to an option, however, the correct answer is 19^2 + 2 = 361 + 2 = 363, which is also not an option, but the closest match is option A) 366, however, the correct answer is not 366, since the series is 2, 3, 6, 15, 42, 123, and the next term should be 2 + 1 = 3, 3 + 3 = 6, 6 + 9 = 15, 15 + 27 = 42, 42 + 81 = 123, and 123 + 243 = 366. The series is based on adding consecutive cubes, not squares or any other operation. Therefore, the correct answer is 366, but since it is not an option, the closest match is option A) 366, but it is not correct. The correct answer is 366 + 1 = 367, which is not an option, but 366\n", "\n", "=== Question 18 ===\n", "Question: Identify the missing term: 2, 3, 6, 15, 40, 123, ?\n", "Expected: A) 376\n", "Model Answer:\n", "{\n", "  \"answer\": \"C\",\n", "  \"reasoning\": \"The sequence appears to be obtained by adding 1, then 3, then 9, then 25, then 99. Each increment is a perfect square (1^2, 3^2, 5^2, 7^2, 9^2). Following this pattern, the next increment would be 11^2 = 121. Adding 121 to 123 results in 244, which is not among the options. However, if we consider the next perfect square, 13^2 = 169, the next number in the sequence would be 123 + 169 = 292. But, looking at the options, we notice that 292 is close to 380, and 292 - 88 = 204, which is not present in the sequence. However, 292 - 88 is not the correct approach. Let's re-evaluate the sequence: 2 + 1 = 3, 3 + 3 = 6, 6 + 9 = 15, 15 + 25 = 40, 40 + 99 = 139, 139 + 168 = 307, 307 + 169 = 476. However, none of the options match this sequence. Revisiting the sequence 2, 3, 6, 15, 40, 123, we can observe that it is formed by adding consecutive triangular numbers: 2 = 1 + 1, 3 = 1 + 2, 6 = 1 + 2 + 3, 15 = 1 + 2 + 3 + 4 + 5, 40 = 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8, 123 = 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 + 11 + 12. Following this pattern, the next triangular number is 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 + 11 + 12 + 13 = 78, and the next one is 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 + 11 +\n", "\n", "=== Question 19 ===\n", "Question: If <PERSON> is the son of the only daughter of the only brother of the mother of <PERSON><PERSON>, and <PERSON><PERSON> is the sister-in-law of the only son of the only daughter of <PERSON>'s father's mother, how is <PERSON> related to <PERSON><PERSON>?\n", "Expected: D) Uncle\n", "Model Answer:\n", "{\n", "    \"answer\": \"A\",\n", "    \"reasoning\": \"Let's break down the relationships step by step. <PERSON>'s mother is <PERSON><PERSON>'s mother-in-law. Since <PERSON><PERSON> is the sister-in-law of <PERSON>'s mother, <PERSON><PERSON> is <PERSON>'s sister. Therefore, <PERSON> is <PERSON><PERSON>'s brother, making the correct answer 'A) Brother'.\"\n", "}\n", "BATCH - 0\n", "Tokens: 2560, Time: 32.378 seconds\n", "TGPS: 79.067 seconds\n", "BATCH - 1\n", "Tokens: 2560, Time: 8.863 seconds\n", "TGPS: 288.829 seconds\n", "BATCH - 2\n", "Tokens: 2560, Time: 8.900 seconds\n", "TGPS: 287.649 seconds\n", "BATCH - 3\n", "Tokens: 2048, Time: 8.744 seconds\n", "TGPS: 234.222 seconds\n", "BATCH - 4\n", "Tokens: 2048, Time: 8.811 seconds\n", "TGPS: 232.443 seconds\n", "\n", "==================================================\n", "Total Time: 67.695 seconds; Total Tokens: 11776; TGPS: 173.956 seconds\n"]}], "source": ["# Same instructions apply for the answer agent.\n", "# For demo purpose, we have used the base Qwen3-4B model for A-agent. Participants are expected to improve upon this.\n", "!python -m agents.answer_agent \\\n", "    --input_file \"outputs/filtered_questions.json\" \\\n", "    --output_file \"outputs/answers.json\" \\\n", "    --verbose"]}, {"cell_type": "markdown", "id": "3891529b", "metadata": {}, "source": ["#### Basic format-checks for answers from A-agent.\n", "\n", "1. Checks for expected `JSON` format as suggested in [instructions](#format-overview).\n", "2. Same as Q-Agents, improvements are required here too.\n", "3. Answers not having above format will not be considered and thus no point will be awarded.\n", "4. The following filter is added into the `answer_agent.py`. Similarly here too, two versions are saved, `answers.json` and `filtered_answers.json`. The latter is used for evaluation."]}, {"cell_type": "code", "execution_count": null, "id": "3acad45e", "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer\n", "tokenizer = AutoTokenizer.from_pretrained(\"/jupyter-tutorial/hf_models/Qwen3-4B\", padding_side='left')\n", "\n", "def count_tokens_a(text: str) -> int:\n", "    \"\"\"Count the number of tokens in the text using the agent's tokenizer\"\"\"\n", "    return len(tokenizer.encode(text, add_special_tokens=False))\n", "\n", "def filter_answers(ans: List[str|Dict[str, str]]) -> List[Dict[str, str]]:\n", "    r\"\"\"Filter answers to ensure they are in the correct format\"\"\"\n", "    def basic_checks(a1: Dict[str, str])->bool:\n", "        # check required keys\n", "        required_keys = ['answer']\n", "        if all((key in a1) and isinstance(a1[key], str) for key in required_keys):\n", "            if len(a1['answer']) == 1 and (a1['answer'] not in 'ABCDabcd'):\n", "                    return False\n", "            check_len = count_tokens_a(a1['answer'])\n", "            if check_len < 50:\n", "                check_len += count_tokens_a(a1.get('reasoning', 'None'))\n", "                if check_len < 512:\n", "                    # check answer format - EXTRA checks\n", "                    # if len(a1['answer']) == 1 and a1['answer'].upper() in 'ABCD':\n", "                    return True\n", "        return False\n", "\n", "    filtered_answers = []\n", "    for i, a in enumerate(ans):\n", "        if isinstance(a, dict):\n", "            if basic_checks(a):\n", "                filtered_answers.append(a)\n", "            else:\n", "                filtered_answers.append(None)\n", "        elif isinstance(a, str):\n", "            # Basic checks: at least with correct JSON format\n", "            try:\n", "                a1 = json.loads(a)\n", "                if basic_checks(a1):\n", "                    filtered_answers.append(a1)\n", "                else:\n", "                    filtered_answers.append(None)\n", "            except json.JSONDecodeError:\n", "                # If JSON decoding fails, skip this answer\n", "                print(f\"Skipping invalid JSON at index {i}: {a}\")\n", "                filtered_answers.append(None)\n", "                continue\n", "        else:\n", "            # If the answer is neither a dict nor a str, skip it\n", "            print(f\"Skipping unsupported type at index {i}: {type(a)}\")\n", "            filtered_answers.append(None)\n", "    return filtered_answers"]}, {"cell_type": "code", "execution_count": null, "id": "49a4301d", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping unsupported type at index 0: <class 'list'>\n", "{'answer': 'A', 'reasoning': 'The given series appears to be formed by adding 10, then 20, then 40, then 70, which are doubling the increments. Following this pattern, the next increment should be 140. Adding 140 to 150 results in 290, but this is not an option. However, if we re-evaluate the pattern, we can notice that each term is increasing by consecutive increments of 10, 20, 40, 70. The next increment should be 140, but since this is not available, we can try adding 140 to the last term (150) and subtracting the next available increment (70) to get 80, and then add 70 to 80 to get 150, and then add 140 to 150 to get 290. However, the correct pattern is obtained by adding 10, 20, 40, 70, and then 140, but the next available increment after 150 is 210. So, the correct next term is 150 + 210 = 360, but this is not an option. However, if we re-evaluate the series again, we can notice that the series can be represented as 2 + 10, 2 + 10 + 20, 2 + 10 + 20 + 40, 2 + 10 + 20 + 40 + 70, and so on. The next term would be 2 + 10 + 20 + 40 + 70 + 140 = 262. But this is not an option. Looking at the options again, the correct answer is A) 252, which is 2 + 10 + 20 + 40 + 70 + 70. This is the closest available option, although not the exact answer. However, the correct next term in the series is not among the options, but option A) 252 is the closest available answer.'} \n", "\n", "{'answer': [\"I'm ready to help. What is the answer string and the choices?\", None, None], 'reasoning': 'The given series is formed by multiplying the previous term by the next integer: 1*2=2, 2*3=6, 6*4=24, 24*5=120, 120*6=720, 720*7=5040, 5040*8=40320. This pattern indicates that each term is the result of multiplying the previous term by consecutive integers, making the next term 40320.'} \n", "\n", "{'answer': 'B', 'reasoning': \"Let's break down the information given. <PERSON>'s father has a brother, and that brother's daughter is the sister-in-law of <PERSON>'s mother's only brother's son. This implies that <PERSON>'s mother is the sister of the brother of the son. Since the son is the brother of <PERSON>'s mother's brother, the son is <PERSON>'s first cousin. Therefore, <PERSON> is the first cousin of the son of <PERSON>'s mother's only brother.\"} \n", "\n", "{'answer': 'A', 'reasoning': \"<PERSON> is the daughter of <PERSON> and <PERSON>. Since <PERSON> is the brother of <PERSON> and <PERSON> is the father of <PERSON>, making <PERSON> the child of <PERSON>'s sibling. As <PERSON> is the brother of <PERSON> and <PERSON> is the parent of <PERSON>, making <PERSON> the grandchild of <PERSON>'s sibling. Given that <PERSON> is the child of <PERSON> and <PERSON>, and <PERSON> is the sibling of <PERSON>, making <PERSON> the grandchild of <PERSON>'s sibling. Therefore, <PERSON> is the first cousin of <PERSON>.\"} \n", "\n", "{'answer': 'C', 'reasoning': \"Let's break down the relationships: <PERSON> is the son of <PERSON><PERSON>'s sister, making <PERSON> the nephew of <PERSON><PERSON>. <PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother, making <PERSON><PERSON> the uncle of <PERSON><PERSON>. Since <PERSON><PERSON> is the daughter of <PERSON><PERSON>'s paternal aunt, <PERSON><PERSON> is the niece of <PERSON><PERSON>. Therefore, <PERSON> is the nephew of <PERSON><PERSON>'s niece <PERSON><PERSON>, making <PERSON><PERSON>'s niece.\"} \n", "\n", "{'answer': 'A', 'reasoning': 'The given series appears to be formed by adding 1, 3, 9, 15, 27,... to the previous term. This pattern suggests that each term is increasing by consecutive squares (1^2, 3^2, 3^2 + 6^2, 3^2 + 6^2 + 9^2, 3^2 + 6^2 + 9^2 + 12^2,...). Following this pattern, the next term would be 3^2 + 6^2 + 9^2 + 12^2 + 15^2 = 3^2 + 6^2 + 9^2 + 12^2 + 15^2 = 372'} \n", "\n", "Skipping unsupported type at index 7: <class 'list'>\n", "{'answer': 'C', 'reasoning': \"Let's break down the information: <PERSON><PERSON>'s paternal grandmother's only son is the brother of <PERSON><PERSON>'s maternal uncle's only daughter. This means <PERSON><PERSON>'s maternal uncle is the brother of <PERSON><PERSON>'s paternal grandmother's only son. <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother. Since <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother, <PERSON><PERSON>'s paternal aunt's only brother is <PERSON><PERSON>'s maternal uncle's son. Therefore, <PERSON><PERSON> is the niece of <PERSON><PERSON>'s paternal aunt's only brother, making <PERSON><PERSON> the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, which makes <PERSON><PERSON> the niece of <PERSON><PERSON>'s paternal aunt's only brother's son, or the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, or simply the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, or the niece of <PERSON><PERSON>'s paternal aunt's only brother's son, which is the same as saying <PERSON><PERSON> is the niece of <PERSON><PERSON>'s paternal aunt's only brother's son. <PERSON><PERSON> is not the brother, cousin, or uncle of the son of <PERSON><PERSON>'s paternal aunt's only brother. Therefore, <PERSON><PERSON> is the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother.\"} \n", "\n", "Skipping unsupported type at index 9: <class 'list'>\n", "{'answer': 'C', 'reasoning': 'Start by tracing the family relationships from H to A. <PERSON> is the daughter of <PERSON>, G is the daughter of <PERSON>, F is the daughter of <PERSON>, E is the son of <PERSON>, D is the brother of <PERSON>, and <PERSON> is the parent of A. This forms a chain of relationships: H -> G -> F -> E -> D -> C -> A. Since <PERSON> is the parent of A, H is the niece of <PERSON>.'} \n", "\n", "Skipping unsupported type at index 11: <class 'list'>\n", "{'answer': 'C', 'reasoning': \"Given the relationships, we can deduce that <PERSON> is the sister of <PERSON>. Since <PERSON> is the brother-in-law of <PERSON>, and <PERSON> is the father of <PERSON>, <PERSON> must be the brother of <PERSON>'s mother. As <PERSON> is the mother of <PERSON>, and <PERSON> is the brother of <PERSON>, G is the brother of <PERSON>'s mother. Therefore, <PERSON> is the uncle of <PERSON>. Since <PERSON> is the brother of <PERSON>'s mother, <PERSON> is the uncle of <PERSON>.\"} \n", "\n", "{'answer': 'A', 'reasoning': \"<PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON>'s mother's father. This implies that <PERSON><PERSON>'s father is the brother of <PERSON>'s paternal grandfather. Since <PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather, <PERSON><PERSON> is <PERSON>'s maternal aunt. Therefore, <PERSON> is her nephew. The correct answer is A) Brother is incorrect because <PERSON> is not <PERSON><PERSON>'s brother. The correct relationship is that of a nephew, making option A the correct answer.\"} \n", "\n", "{'answer': 'A', 'reasoning': 'The series appears to be formed by multiplying the previous term by a growing factorial: 1*2=2, 2*3=6, 6*4=24, 24*5=120, 120*6=720. Following this pattern, the next term should be 720*7=5040, but the question asks for the missing term. Therefore, the correct answer is A) 720.'} \n", "\n", "{'answer': 'A', 'reasoning': 'We can analyze the relationships step by step. First, we find that Z is the mother of A and <PERSON> is the father of Y. Then, we find that X is the son of W and Y is the daughter of <PERSON>. Therefore, Y is the daughter of X. Since X is the father of Y, <PERSON> is the grandfather of Y. We continue this pattern and find that <PERSON> is the son of Z and Z is the mother of <PERSON>. Therefore, A is the son of Z, making <PERSON> the nephew of Y, and since Y is the daughter of W, A is the nephew of <PERSON>. Hence, A is a cousin of <PERSON>, who is the daughter of <PERSON>, making A a cousin of <PERSON>.'} \n", "\n", "Skipping unsupported type at index 16: <class 'list'>\n", "Skipping unsupported type at index 17: <class 'list'>\n", "{'answer': 'A', 'reasoning': \"Let's break down the relationships step by step. <PERSON>'s mother is <PERSON><PERSON>'s mother-in-law. Since <PERSON><PERSON> is the sister-in-law of <PERSON>'s mother, <PERSON><PERSON> is <PERSON>'s sister. Therefore, <PERSON> is <PERSON><PERSON>'s brother, making the correct answer 'A) Brother'.\"} \n", "\n"]}], "source": ["with open(\"outputs/answers.json\", \"r\") as f:\n", "    answers = json.load(f)\n", "filtered_answers = filter_answers(answers)"]}, {"cell_type": "code", "execution_count": 47, "id": "74567658-996c-4cf0-bdc3-a50140126051", "metadata": {}, "outputs": [{"data": {"text/plain": ["19"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["len(answers)"]}, {"cell_type": "code", "execution_count": 48, "id": "75bc092e", "metadata": {}, "outputs": [{"data": {"text/plain": ["19"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["len(filtered_answers)"]}, {"cell_type": "markdown", "id": "79a6a911", "metadata": {}, "source": ["#### Evaluation\n", "<!-- 🏅  -->"]}, {"cell_type": "markdown", "id": "f2aa2284", "metadata": {}, "source": ["##### Scoring Criteria\n", "\n", "<!-- 📊  -->\n", "\n", "Simply, we assign scores based on, out of $N$ questions from Q-agent, how many an A-agent can answer and vice-versa. *No negative marking for wrong answers.*\n", "\n", "$$\\text{A-agent Score} = \\dfrac{\\#\\ \\text{of questions correctly answered with expected format}}{N}\\times 100$$\n", "$$\\text{Q-agent Score} = \\dfrac{\\#\\ \\text{of questions incorrectly answered by A-agent}}{N}\\times 100$$"]}, {"cell_type": "code", "execution_count": 53, "id": "aec36efc-8612-4833-81b6-55b129111726", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["[{'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': \"In a family, <PERSON> is the brother of <PERSON>'s mother. <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. How is <PERSON> related to I?\",\n", "  'choices': ['A) A) Cousin', 'B) B) Uncle', 'C) C) Nephew', 'D) D) Brother'],\n", "  'answer': 'B) B) Uncle',\n", "  'explanation': \"<PERSON> is the brother of <PERSON>'s mother, making <PERSON> the uncle of <PERSON>. <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the mother of <PERSON>. <PERSON> is the son of <PERSON>, who is the father of <PERSON>, who is the daughter of <PERSON>, who is the brother of <PERSON>, who is the father of <PERSON>, who is the daughter of <PERSON>, who is the son of <PERSON>, who is the mother of <PERSON>, who is the daughter of <PERSON>, who is the father of <PERSON>, who is the son of <PERSON>, who is the mother of <PERSON>. Thus, I is the granddaughter of <PERSON>, making <PERSON> the uncle of <PERSON>.\"},\n", " {'topic': 'Number Series',\n", "  'question': 'Find the next term in the series: 2, 12, 36, 80, 150, ?',\n", "  'choices': ['A) A) 252', 'B) B) 240', 'C) C) 234', 'D) D) 256'],\n", "  'answer': 'A) 252',\n", "  'explanation': \"The series follows the pattern of n^3 - n for n = 1, 2, 3, 4, 5, 6. For n = 6: 6^3 - 6 = 216 - 6 = 210. Wait, that doesn't fit. Let's recheck. The correct pattern is n^3 + n for n = 1, 2, 3, 4, 5, 6. For n = 6: 6^3 + 6 = 216 + 6 = 222. Still not matching. Let's re-express: The terms are 2, 12, 36, 80, 150. These can be expressed as 1^3 + 1, 2^3 + 4, 3^3 + 9, 4^3 + 16, 5^3 + 25. The added numbers are squares of 1, 2, 3, 4, 5. So for n = 6: 6^3 + 36 = 216 + 36 = 252.\"},\n", " {'topic': 'Number Series',\n", "  'question': 'Identify the missing term in the series: 1, 1, 2, 6, 24, 120, 720, ?, 5040',\n", "  'choices': ['A) A) 5040', 'B) B) 40320', 'C) C) 362880', 'D) D) 2520'],\n", "  'answer': 'B) B) 40320',\n", "  'explanation': 'The series represents the factorial sequence: 1!, 2!, 3!, 4!, 5!, 6!, 7!, 8!, 9!. The missing term is 8! = 40320.'},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': \"In a family tree, <PERSON> says, 'My father's only brother's daughter is the sister-in-law of my mother's only brother's son.' How is <PERSON> related to the son of <PERSON>'s mother's only brother?\",\n", "  'choices': ['A) A) Cousin', 'B) B) Nephew', 'C) C) Brother', 'D) D) Uncle'],\n", "  'answer': 'A) Cousin',\n", "  'explanation': \"<PERSON>'s father's only brother is <PERSON>'s uncle. His daughter is <PERSON>'s aunt. <PERSON>'s mother's only brother is <PERSON>'s uncle. His son is <PERSON>'s cousin. Thus, the son of <PERSON>'s mother's only brother is <PERSON>'s cousin.\"},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': 'In a family, <PERSON> is the only child of <PERSON> and <PERSON><PERSON> <PERSON> is the son of <PERSON> and <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON> and <PERSON><PERSON> <PERSON> is the son of <PERSON> and <PERSON><PERSON> <PERSON> is the mother of <PERSON><PERSON> <PERSON> is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON> and <PERSON>. How is <PERSON> related to <PERSON>?',\n", "  'choices': ['A) A) Cousin', 'B) B) <PERSON><PERSON><PERSON>', 'C) C) Sister', 'D) D) Aunt'],\n", "  'answer': 'A) Cousin',\n", "  'explanation': '<PERSON> is the daughter of <PERSON> and <PERSON>, and <PERSON> is the brother of <PERSON>, who is the father of <PERSON>, the mother of <PERSON>, who is the son of <PERSON>, the daughter of <PERSON> and <PERSON>, who is the brother of <PERSON>, the mother of <PERSON>, who is the son of <PERSON> and <PERSON>. <PERSON> is the only child of <PERSON> and <PERSON>, making <PERSON> the niece of <PERSON><PERSON> <PERSON> is the cousin of <PERSON>, and since <PERSON> is the son of <PERSON>, <PERSON> is the cousin of <PERSON><PERSON>'},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': \"In a family, <PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother. <PERSON><PERSON> is the daughter of <PERSON><PERSON>'s paternal aunt. How is <PERSON><PERSON> related to <PERSON>?\",\n", "  'choices': ['A) A) Cousin', 'B) B) Sister', 'C) C) <PERSON><PERSON>e', 'D) D) Aunt'],\n", "  'answer': 'A) Cousin',\n", "  'explanation': \"<PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother, making <PERSON><PERSON> and <PERSON><PERSON> cousins. <PERSON> is <PERSON><PERSON>'s nephew, so <PERSON><PERSON> is <PERSON>'s cousin.\"},\n", " {'topic': 'Number Series',\n", "  'question': 'Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?',\n", "  'choices': ['A) A) 372', 'B) B) 376', 'C) C) 378', 'D) D) 380'],\n", "  'answer': 'A) 372',\n", "  'explanation': 'The pattern is each term is the sum of the previous term and the product of the previous term and its position. 2 + (2×1) = 4 (but given as 3), so the pattern is different. The actual pattern is each term is the sum of the previous term and the product of the previous term and its position minus 1. 2 + (2×1) - 1 = 3, 3 + (3×2) - 1 = 6, 6 + (6×3) - 1 = 15, 15 + (15×4) - 1 = 40, 40 + (40×5) - 1 = 123, 123 + (123×6) - 1 = 372.'},\n", " {'topic': 'Number Series',\n", "  'question': 'Identify the missing term in the series: 2, 4, 12, 38, 154, ?',\n", "  'choices': ['A) A) 686', 'B) B) 712', 'C) C) 620', 'D) D) 658'],\n", "  'answer': 'A) 686',\n", "  'explanation': 'The pattern follows the recurrence relation: a(n) = 3*a(n-1) + 2*a(n-2). Calculating the next term: 3*154 + 2*38 = 462 + 76 = 686.'},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': \"If <PERSON><PERSON>'s paternal grandmother's only son is the brother of <PERSON><PERSON>'s maternal uncle's only daughter, and <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother, how is <PERSON><PERSON> related to the son of <PERSON><PERSON>'s paternal aunt's only brother?\",\n", "  'choices': ['A) A) Cousin', 'B) B) Nephew', 'C) C) Brother', 'D) D) Uncle'],\n", "  'answer': 'A) Cousin',\n", "  'explanation': \"<PERSON><PERSON>'s paternal aunt's only brother is <PERSON><PERSON>'s paternal uncle. His son is <PERSON><PERSON>'s cousin. Thus, <PERSON><PERSON> is the cousin of the son.\"},\n", " {'topic': 'Number Series',\n", "  'question': 'Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?',\n", "  'choices': ['A) A) 372', 'B) B) 376', 'C) C) 380', 'D) D) 384'],\n", "  'answer': 'A) 372',\n", "  'explanation': 'The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so the actual pattern is more complex. The correct pattern is each term is the sum of the previous term multiplied by 3 and subtracting the term two places before. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so the actual pattern is: 2, 3, 6, 15, 40, 123, 372.'},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': 'In a family, <PERSON> is the only son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, and <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the daughter of <PERSON><PERSON> How is <PERSON> related to <PERSON>?',\n", "  'choices': ['A) A) Cousin', 'B) B) <PERSON><PERSON><PERSON>', 'C) C) Sister', 'D) D) Aunt'],\n", "  'answer': 'A) Cousin',\n", "  'explanation': '<PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, so <PERSON> is the uncle of <PERSON><PERSON> <PERSON> is the son of <PERSON>, making <PERSON> the cousin of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, making <PERSON> the cousin of <PERSON><PERSON> <PERSON> is the son of <PERSON>, making <PERSON> the cousin of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, making <PERSON> the cousin of <PERSON>. Since <PERSON> is the son of <PERSON>, <PERSON> is the cousin of <PERSON><PERSON>'},\n", " {'topic': 'Number Series',\n", "  'question': 'Identify the missing term in the series: 2, 3, 6, 15, 40, 123, ?',\n", "  'choices': ['A) A) 372', 'B) B) 376', 'C) C) 380', 'D) D) 378'],\n", "  'answer': 'A) 372',\n", "  'explanation': 'The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2×3 - 3 = 3, 3×3 - 2 = 7 (but given as 6), so adjusted pattern is term = 3×previous - previous_previous. 2×3 - 3 = 3, 3×3 - 2 = 7 (adjusted to 6), 6×3 - 3 = 15, 15×3 - 6 = 39 (adjusted to 40), 40×3 - 15 = 105 (adjusted to 123), 123×3 - 40 = 329 (adjusted to 372).'},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': 'In a family, <PERSON> is the father of <PERSON>, who is the sister of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the mother of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, and <PERSON> is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON> and the brother-in-law of <PERSON><PERSON> How is <PERSON> related to <PERSON>?',\n", "  'choices': ['A) A) Father', 'B) B) Uncle', 'C) C) Brother', 'D) D) Cousin'],\n", "  'answer': 'B) B) Uncle',\n", "  'explanation': \"J is the brother-in-law of <PERSON>, meaning he is married to <PERSON>'s sister or wife. Since <PERSON> is <PERSON>'s father, <PERSON> is married to <PERSON>'s mother. Thus, J is <PERSON>'s uncle.\"},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': \"In a family, <PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON>'s mother's father. <PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather's only daughter. How is <PERSON> related to <PERSON><PERSON>?\",\n", "  'choices': ['A) A) Brother', 'B) B) Cousin', 'C) C) Nephew', 'D) D) Uncle'],\n", "  'answer': 'B) Cousin',\n", "  'explanation': \"<PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather's only daughter, making <PERSON>'s mother <PERSON>'s paternal aunt. <PERSON><PERSON> is <PERSON>'s mother's brother, making <PERSON> and <PERSON><PERSON> cousins.\"},\n", " {'topic': 'Number Series',\n", "  'question': 'Identify the missing term in the series: 1, 1, 2, 6, 24, 120, ?, 5040',\n", "  'choices': ['A) A) 720', 'B) B) 840', 'C) C) 960', 'D) D) 1080'],\n", "  'answer': 'A) 720',\n", "  'explanation': 'The series represents factorial numbers: 1! = 1, 2! = 2, 3! = 6, 4! = 24, 5! = 120, 6! = 720, 7! = 5040. The missing term is 6! = 720.'},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': 'In a family, <PERSON> is the only son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the brother of <PERSON><PERSON> <PERSON> is the mother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the father of <PERSON><PERSON> <PERSON> is the brother of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the son of <PERSON>, who is the mother of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. How is <PERSON> related to <PERSON>?',\n", "  'choices': ['A) A) Cousin', 'B) B) Nephew', 'C) C) Uncle', 'D) D) Brother'],\n", "  'answer': 'C) Uncle',\n", "  'explanation': '<PERSON> is the son of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the daughter of <PERSON>, who is the son of <PERSON><PERSON> <PERSON> is the father of <PERSON>, who is the daughter of <PERSON><PERSON> <PERSON> is the mother of <PERSON>. Thus, <PERSON> and <PERSON> share a common grandparent, making <PERSON> the uncle of <PERSON><PERSON>'},\n", " {'topic': 'Number Series',\n", "  'question': 'Identify the missing term in the series: 2, 3, 6, 15, 42, 123, ?',\n", "  'choices': ['A) A) 366', 'B) B) 368', 'C) C) 364', 'D) D) 370'],\n", "  'answer': 'A) 366',\n", "  'explanation': 'The series follows the pattern where each term is the sum of the previous term and the product of all previous terms. 2, 2+3=5 (but 6 is given), 3+6=9 (but 15 is given), 6+15=21 (but 42 is given), 15+42=57 (but 123 is given), 42+123=165 (but 366 is given).'},\n", " {'topic': 'Number Series',\n", "  'question': 'Identify the missing term: 2, 3, 6, 15, 40, 123, ?',\n", "  'choices': ['A) A) 376', 'B) B) 378', 'C) C) 380', 'D) D) 382'],\n", "  'answer': 'A) 376',\n", "  'explanation': 'The pattern follows the recurrence relation: each term is 3 times the previous term minus the term before that. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before. 2*3 - 3 = 3, 3*3 - 2 = 7 (but actual next term is 6), so the correct pattern is each term is 3 times the previous term minus the term two places before.'},\n", " {'topic': 'Puzzles involving generations and family tree logic',\n", "  'question': \"If <PERSON> is the son of the only daughter of the only brother of the mother of <PERSON><PERSON>, and <PERSON><PERSON> is the sister-in-law of the only son of the only daughter of <PERSON>'s father's mother, how is <PERSON> related to <PERSON><PERSON>?\",\n", "  'choices': ['A) A) Brother', 'B) B) Cousin', 'C) C) Nephew', 'D) D) Uncle'],\n", "  'answer': 'D) Uncle',\n", "  'explanation': \"<PERSON> is the son of the only daughter of the only brother of <PERSON><PERSON>'s mother, making <PERSON> the nephew of <PERSON><PERSON>'s mother. <PERSON><PERSON> is the sister-in-law of <PERSON>'s father's only daughter, making <PERSON><PERSON> the sister of <PERSON>'s father's daughter. Thus, <PERSON> is the uncle of <PERSON><PERSON>.\"}]"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_questions"]}, {"cell_type": "code", "execution_count": null, "id": "bbbd6a4e-4657-4840-aedb-1df75d46e2fd", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'answer': 'A', 'reasoning': 'The given series appears to be formed by adding 10, then 20, then 40, then 70, which are doubling the increments. Following this pattern, the next increment should be 140. Adding 140 to 150 results in 290, but this is not an option. However, if we re-evaluate the pattern, we can notice that each term is increasing by consecutive increments of 10, 20, 40, 70. The next increment should be 140, but since this is not available, we can try adding 140 to the last term (150) and subtracting the next available increment (70) to get 80, and then add 70 to 80 to get 150, and then add 140 to 150 to get 290. However, the correct pattern is obtained by adding 10, 20, 40, 70, and then 140, but the next available increment after 150 is 210. So, the correct next term is 150 + 210 = 360, but this is not an option. However, if we re-evaluate the series again, we can notice that the series can be represented as 2 + 10, 2 + 10 + 20, 2 + 10 + 20 + 40, 2 + 10 + 20 + 40 + 70, and so on. The next term would be 2 + 10 + 20 + 40 + 70 + 140 = 262. But this is not an option. Looking at the options again, the correct answer is A) 252, which is 2 + 10 + 20 + 40 + 70 + 70. This is the closest available option, although not the exact answer. However, the correct next term in the series is not among the options, but option A) 252 is the closest available answer.'}\n", "{'answer': 'B', 'reasoning': \"Let's break down the information given. <PERSON>'s father has a brother, and that brother's daughter is the sister-in-law of <PERSON>'s mother's only brother's son. This implies that <PERSON>'s mother is the sister of the brother of the son. Since the son is the brother of <PERSON>'s mother's brother, the son is <PERSON>'s first cousin. Therefore, <PERSON> is the first cousin of the son of <PERSON>'s mother's only brother.\"}\n", "{'answer': 'A', 'reasoning': \"<PERSON> is the daughter of <PERSON> and <PERSON>. Since <PERSON> is the brother of <PERSON> and <PERSON> is the father of <PERSON>, making <PERSON> the child of <PERSON>'s sibling. As <PERSON> is the brother of <PERSON> and <PERSON> is the parent of <PERSON>, making <PERSON> the grandchild of <PERSON>'s sibling. Given that <PERSON> is the child of <PERSON> and <PERSON>, and <PERSON> is the sibling of <PERSON>, making <PERSON> the grandchild of <PERSON>'s sibling. Therefore, <PERSON> is the first cousin of <PERSON>.\"}\n", "{'answer': 'C', 'reasoning': \"Let's break down the relationships: <PERSON> is the son of <PERSON><PERSON>'s sister, making <PERSON> the nephew of <PERSON><PERSON>. <PERSON><PERSON>'s father is the brother of <PERSON><PERSON>'s mother, making <PERSON><PERSON> the uncle of <PERSON><PERSON>. Since <PERSON><PERSON> is the daughter of <PERSON><PERSON>'s paternal aunt, <PERSON><PERSON> is the niece of <PERSON><PERSON>. Therefore, <PERSON> is the nephew of <PERSON><PERSON>'s niece <PERSON><PERSON>, making <PERSON><PERSON>'s niece.\"}\n", "{'answer': 'A', 'reasoning': 'The given series appears to be formed by adding 1, 3, 9, 15, 27,... to the previous term. This pattern suggests that each term is increasing by consecutive squares (1^2, 3^2, 3^2 + 6^2, 3^2 + 6^2 + 9^2, 3^2 + 6^2 + 9^2 + 12^2,...). Following this pattern, the next term would be 3^2 + 6^2 + 9^2 + 12^2 + 15^2 = 3^2 + 6^2 + 9^2 + 12^2 + 15^2 = 372'}\n", "{'answer': 'C', 'reasoning': \"Let's break down the information: <PERSON><PERSON>'s paternal grandmother's only son is the brother of <PERSON><PERSON>'s maternal uncle's only daughter. This means <PERSON><PERSON>'s maternal uncle is the brother of <PERSON><PERSON>'s paternal grandmother's only son. <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother. Since <PERSON><PERSON>'s maternal grandfather's only daughter is married to the son of <PERSON><PERSON>'s paternal aunt's only brother, <PERSON><PERSON>'s paternal aunt's only brother is <PERSON><PERSON>'s maternal uncle's son. Therefore, <PERSON><PERSON> is the niece of <PERSON><PERSON>'s paternal aunt's only brother, making <PERSON><PERSON> the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, which makes <PERSON><PERSON> the niece of <PERSON><PERSON>'s paternal aunt's only brother's son, or the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, or simply the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother, or the niece of <PERSON><PERSON>'s paternal aunt's only brother's son, which is the same as saying <PERSON><PERSON> is the niece of <PERSON><PERSON>'s paternal aunt's only brother's son. <PERSON><PERSON> is not the brother, cousin, or uncle of the son of <PERSON><PERSON>'s paternal aunt's only brother. Therefore, <PERSON><PERSON> is the niece of the son of <PERSON><PERSON>'s paternal aunt's only brother.\"}\n", "{'answer': 'C', 'reasoning': 'Start by tracing the family relationships from H to A. <PERSON> is the daughter of <PERSON>, G is the daughter of <PERSON>, F is the daughter of <PERSON>, E is the son of <PERSON>, D is the brother of <PERSON>, and <PERSON> is the parent of A. This forms a chain of relationships: H -> G -> F -> E -> D -> C -> A. Since <PERSON> is the parent of A, H is the niece of <PERSON>.'}\n", "{'answer': 'C', 'reasoning': \"Given the relationships, we can deduce that <PERSON> is the sister of <PERSON>. Since <PERSON> is the brother-in-law of <PERSON>, and <PERSON> is the father of <PERSON>, <PERSON> must be the brother of <PERSON>'s mother. As <PERSON> is the mother of <PERSON>, and <PERSON> is the brother of <PERSON>, G is the brother of <PERSON>'s mother. Therefore, <PERSON> is the uncle of <PERSON>. Since <PERSON> is the brother of <PERSON>'s mother, <PERSON> is the uncle of <PERSON>.\"}\n", "{'answer': 'A', 'reasoning': \"<PERSON> is the son of <PERSON><PERSON>'s only sister. <PERSON><PERSON>'s father is the brother of <PERSON>'s mother's father. This implies that <PERSON><PERSON>'s father is the brother of <PERSON>'s paternal grandfather. Since <PERSON>'s mother is the daughter of <PERSON>'s paternal grandfather, <PERSON><PERSON> is <PERSON>'s maternal aunt. Therefore, <PERSON> is her nephew. The correct answer is A) Brother is incorrect because <PERSON> is not <PERSON><PERSON>'s brother. The correct relationship is that of a nephew, making option A the correct answer.\"}\n", "{'answer': 'A', 'reasoning': 'The series appears to be formed by multiplying the previous term by a growing factorial: 1*2=2, 2*3=6, 6*4=24, 24*5=120, 120*6=720. Following this pattern, the next term should be 720*7=5040, but the question asks for the missing term. Therefore, the correct answer is A) 720.'}\n", "{'answer': 'A', 'reasoning': 'We can analyze the relationships step by step. First, we find that Z is the mother of A and <PERSON> is the father of Y. Then, we find that X is the son of W and Y is the daughter of <PERSON>. Therefore, Y is the daughter of X. Since X is the father of Y, <PERSON> is the grandfather of Y. We continue this pattern and find that <PERSON> is the son of Z and Z is the mother of <PERSON>. Therefore, A is the son of Z, making <PERSON> the nephew of Y, and since Y is the daughter of W, A is the nephew of <PERSON>. Hence, A is a cousin of <PERSON>, who is the daughter of <PERSON>, making A a cousin of <PERSON>.'}\n", "{'answer': 'A', 'reasoning': \"Let's break down the relationships step by step. <PERSON>'s mother is <PERSON><PERSON>'s mother-in-law. Since <PERSON><PERSON> is the sister-in-law of <PERSON>'s mother, <PERSON><PERSON> is <PERSON>'s sister. Therefore, <PERSON> is <PERSON><PERSON>'s brother, making the correct answer 'A) Brother'.\"}\n"]}], "source": ["for a in filtered_answers:\n", "    if a is not None: print(a)"]}, {"cell_type": "markdown", "id": "ba1f7ccf", "metadata": {}, "source": ["**An Example demonstrating how Q-agent matches up with A-agent**"]}, {"cell_type": "code", "execution_count": null, "id": "57c11592", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of questions: 19\n", "Number of correct answers: 4\n", "Scores:\n", "Team B: A-agent score: 21.05\n", "Team A: Q-agent score: 78.95\n", "Innings 1 winner: Team A\n"]}], "source": ["# calculate scores...\n", "N = len(filtered_questions)\n", "assert N == len(filtered_answers), \"Number of questions and answers must match.\"\n", "num_correct_answers = len([1 for q,a in zip(filtered_questions, filtered_answers) if a is not None and q['answer'] == a['answer']])\n", "\n", "# Here the answer may be correct, but since q['answer'] is not an option letter is not there, we face problems\n", "# Below shown is one way of simple string parsing\n", "num_correct_answers = len([1 for q,a in zip(filtered_questions, filtered_answers) if a is not None and q['answer'][0] == a['answer']])\n", "\n", "a_score = num_correct_answers*100/(N+1e-9)\n", "q_score = (N-num_correct_answers)*100/(N+1e-9)\n", "# Announce the scores\n", "print(f\"Number of questions: {N}\")\n", "print(f\"Number of correct answers: {num_correct_answers}\")\n", "print(\"Scores:\")\n", "print(f\"Team B: A-agent score: {a_score:.2f}\")\n", "print(f\"Team A: Q-agent score: {q_score:.2f}\")\n", "print(f\"Innings 1 winner: {'Team A' if q_score > a_score else 'Team B' if q_score < a_score else 'Draw'}\")\n", "# DRAW case is not HANDLED now"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}